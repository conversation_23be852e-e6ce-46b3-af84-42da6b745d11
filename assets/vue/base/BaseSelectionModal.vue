<template>
  <div class="BaseSelectionModal">
    <BaseModal :identifier="id" @close="closeModal" :title="$t(title)">
      <div class="input-group mb-3">
        <div class="input-group-prepend">
          <span class="input-group-text h-100"><i class="fas fa-search content-search-icon"/></span>
        </div>
        <input type="text" class="form-control" :placeholder="$t(searchText)" v-model="filter"
               @input="onFilterInput" />
      </div>
      
      <p class="info" v-if="totalItems">{{ $t('COMMON.PAGINATION_INFO', [list.length, totalItems]) }}</p>
      
      <DataNotFound v-if="!list.length" :hide-on="!list.length"
        :text="$t(notFoundText)" :icon="notFoundIcon" :banner="true" />
      
      
      <div class="item-list">
        <ItemSelectionCard
          v-for="item in list"
          :key="item.key"
          :item="item"
          :isSelected="isSelected(item.id)"
          :button-text="buttonText"
          :selected-text="selectedText"
          @add="$emit('add', item)"
        />
      </div>
    </BaseModal>
  </div>
</template>
<script>
import debounceMixin from '../announcement/mixins/debounceMixin'
import BaseModal from './BaseModal.vue'
import Spinner from "../admin/components/base/Spinner.vue";
import DataNotFound from '../announcement/components/details/DataNotFound.vue'
import ItemSelectionCard from './ItemSelectionCard.vue';

export default {
  name: "BaseSelectionModal",
  mixins: [debounceMixin],
  components: { BaseModal, Spinner, DataNotFound, ItemSelectionCard },
  props: {
    id: {
      type: String,
      default: 'BaseSelectionModal'
    },
    title: {
      type: String,
      default: 'ANNOUNCEMENT.SHARETAB.MODAL_TITLE'
    },
    searchText: {
      type: String,
      default: 'ANNOUNCEMENT.SHARETAB.FILTER.SEARCH'
    },
    notFoundText: {
      type: String,
      default: 'ANNOUNCEMENT.SHARETAB.FILTER.NOT_FOUND'
    },
    buttonText: {
      type: String,
      default: 'ANNOUNCEMENT.SHARETAB.TITLE'
    },
    selectedText: {
      type: String,
      default: 'ANNOUNCEMENT.SHARETAB.ALREADY_SHARED'
    },
    notFoundIcon: {
      type: String,
      default: 'fa-users'
    },
    list: {
      type: Array,
      default: () => []
    },
    excludedItems: {
      type: Array,
      default: () => []
    },
    totalItems: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      isSearching: false,
      filter: '',
    }
  },
  methods: {
    isSelected(userId) {
      return this.excludedItems.includes(userId);
    },
    onFilterInput() {
      this.isSearching = true;
      this.debounce(() => {
        this.$emit('search', this.filter);
        this.isSearching = false;
      }, 600, 'search');
    },
    clearData() {
      this.filter = '';
    },
    closeModal() {
      this.clearData();
    },
  },
}

</script>
<style scoped lang="scss">
.BaseSelectionModal {
  :deep(.modal-body) {
    background-color: #eee;
    padding: 1rem !important;
  }
  .content-search-label {
    width: calc(100% - 26px);
  }
  .info {
    text-align: right;
    padding: 0 0.5rem;
    margin: 0 0 0.5rem;
  }
  .item-list {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
  }
}
</style>