<template>
  <div class="studentsTable">
    <table class="table table-condensed">
      <thead>
        <tr>
          <th></th>
          <th></th>
          <th>{{ $t("NAME") }}</th>
          <th v-if="showConfirmation">
            <label-with-info
              location="right"
              :info="$t('ANNOUNCEMENT.FORM.STEPS.CONFIRMATION') + ''"
            >
              {{ $t("ANNOUNCEMENT.STUDENTTAB.CONFIRMATION") }}
            </label-with-info>
          </th>
          <th v-if="hideInPresential" class="text-center">
            {{ $t("CONNECTION") }}
          </th>
          <th v-if="hideInTeleformation" class="text-center">
            {{ $t("ANNOUNCEMENT.ASSISTANCE") }}
          </th>
          <th v-if="hideInPresential" class="text-center">
            {{ $t("PROGRESS") }}
          </th>
          <th v-if="showDetails && hideInPresential" class="text-center">
            {{ $t("ANNOUNCEMENT.FORM.ENTITY.DIDACTIC_GUIDE") }}
          </th>
          <th v-if="showDetails && hasSurvey" class="text-center">
            {{ $t("ANNOUNCEMENT.FORM.STEPS.SURVEY") }}
          </th>
          <th v-if="showDetails && hideInPresential" class="text-center">
            {{ $t("ANNOUNCEMENT.FORM.STEPS.COMMUNICATION") }}
          </th>
          <th class="text-center">{{ $t("ACTIONS") }}</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(student, index) in paginatedList" :key="tag + index">
          <td class="text-center pl-0 pr-0" style="width: 35px;">
            <span class="badge bg-warning text-white" v-if="student.external">EXT</span>
          </td>
          <td
            class="avatarContainer cursor-pointer"
            data-bs-toggle="modal"
            data-bs-target="#userAlerts"
            @click="setUser(index)"
          >
            <i
              class="fa fa-exclamation"
              v-show="
                student.comunications?.alerts?.length ||
                uncompletedUserProfile.includes(student.id)
              "
            ></i>
            <img class="userAvatar" :src="student.avatar || ''" alt="" />
          </td>

          <td
            class="underline cursor-pointer"
            :class="student.validated ? '' : 'danger'"
            @click="setUser(index)"
            data-bs-toggle="modal"
            data-bs-target="#userAlerts"
          >
            <span :class="student.validated ? '' : 'text-danger'">{{
              student.name
            }}</span>
          </td>
          <td class="text-center" v-if="showConfirmation">
            <span
              class="badge text-white"
              :class="
                student.hasConfirmationAssistance ? 'bg-success' : 'bg-danger'
              "
              >{{ $t(student.hasConfirmationAssistance ? "YES" : "NO") }}</span
            >
          </td>
          <td
            @click="setUser(index)"
            class="text-center"
            v-if="hideInPresential"
          >
            <span
              class="underline text-primary cursor-pointer"
              data-bs-toggle="modal"
              data-bs-target="#userConection"
            >
              {{ student.conexions?.length || 0 }}
            </span>
          </td>
          <td v-if="hideInTeleformation">
            <progress-bar
              :value="student.progressAssistancePress || 0"
              :maxSize="50"
              :show-text="false"
            />
          </td>
          <td @click="setUser(index)" v-if="hideInPresential">
            <progress-bar
              :value="student.progressTotal || 0"
              :show-text="true"
              :maxSize="50"
              data-bs-toggle="modal"
              data-bs-target="#userProgress"
            />
          </td>
          <td v-if="showDetails && hideInPresential">
            <div class="d-flex justify-content-center align-items-center">
              <icon-notification
                icon="fa-book"
                :value="student.isReadDidacticGuide ? 0 : 1"
              />
            </div>
          </td>
          <td v-if="showDetails && hasSurvey">
            <div class="d-flex justify-content-center align-items-center">
              <icon-notification
                icon="fa-clipboard-check"
                :value="student.isSurveyCompleted ? 0 : 1"
              />
            </div>
          </td>
          <td v-if="showDetails && hideInPresential" @click="setUser(index)">
            <div
              class="comunicationIcons d-flex justify-content-center align-items-center"
            >
              <router-link
                v-if="allowActions"
                :to="{
                  name: 'ViewAnnouncementTaskDetails',
                  params: { id: announcement.id, studentId: student.id },
                }"
              >
                <icon-notification
                  icon="fa-tasks"
                  :value="
                    (student.comunications?.tasks || []).filter(
                      (task) => task.descriptiveState === 'DELIVERED'
                    ).length || 0
                  "
                  :show-value="true"
                />
              </router-link>
              <icon-notification
                v-if="showChat"
                icon="fa-comment-alt"
                :value="student.unseenMessages ?? 0"
                :show-value="true"
                class="cursor-pointer"
                data-bs-toggle="modal"
                data-bs-target="#userChat"
              />
              <icon-notification
                icon="fa-envelope"
                :value="student.comunications?.notification?.length || 0"
                :show-value="true"
                class="cursor-pointer"
                data-bs-toggle="modal"
                data-bs-target="#userNotifications"
              />
            </div>
          </td>
          <td class="text-center">
            <div class="dropdown m-auto">
              <button
                class="btn btn-default"
                type="button"
                :id="`dropdown-menu-t${tag}-i${index}`"
                data-bs-toggle="dropdown"
                aria-expanded="false"
              >
                <i class="fa fa-ellipsis-h"></i>
              </button>
              <ul
                class="dropdown-menu text-center"
                :aria-labelledby="`dropdown-menu-t${tag}-i${index}`"
              >
                <li>
                  <a
                    class="dropdown-item d-block cursor-pointer"
                    @click="showReport(student, announcement.id)"
                    >{{ $t("ANNOUNCEMENT.STUDENTTAB.VIEW_REPORT") }}
                  </a>
                </li>
                <li v-if="hasBonification && !inspector">
                  <a
                    data-bs-toggle="modal"
                    data-bs-target="#UserFieldsFundaeModal-fill"
                    class="dropdown-item d-block cursor-pointer"
                    @click="fillUserProfile(student)"
                    >{{ $t("USER.USER_FIELDS_FUNDAE.TITLE") }}
                  </a>
                </li>
                <li v-if="!student.validated && !inspector">
                  <a
                    data-bs-toggle="modal"
                    data-bs-target="#user-email-validation-account"
                    class="dropdown-item d-block cursor-pointer"
                    @click="fillUserProfile(student)"
                  >
                    {{ $t("ANNOUNCEMENT.EMAIL.VALIDATE_ACCOUNT") }}
                  </a>
                </li>
                <li v-if="allowRemove">
                  <span
                    class="dropdown-item d-block cursor-pointer bg-danger text-white"
                    @click="openDeleteDialog(student, announcement.id)"
                    data-bs-toggle="modal"
                    data-bs-target="#modal-delete"
                  >{{ $t("DELETE") }}
                  </span>
                </li>
              </ul>
            </div>
          </td>
        </tr>
      </tbody>
    </table>
    <pagination
      v-show="pageSize < studentList.length"
      :items="studentsFiltered"
      :page-size="pageSize"
      @items-page="paginatedList = $event"
    />
  </div>
</template>

<script>
import ProgressBar from "./progressBar";
import IconNotification from "./iconNotification";
import Pagination from "../../../admin/components/Pagination";
import { get, sync } from "vuex-pathify";
import { UtilsMixin } from "../../mixins/utilsMixin";
import LabelWithInfo from "../../../common/components/ui/LabelWithInfo.vue";
import {ANNOUNCEMENT_SOURCE} from "../../store/module/announcementForm/common";

export default {
  name: "studentsTable",
  mixins: [UtilsMixin],
  components: { LabelWithInfo, Pagination, IconNotification, ProgressBar },
  props: {
    tag: {
      type: String,
      default: "group0",
    },
    allowActions: {
      type: Boolean,
      default: true,
    },
    showDetails: {
      type: Boolean,
      default: true,
    },
    studentList: {
      type: Array,
      default: () => [],
    },
    query: {
      type: String,
      default: "",
    },
    inspector: {
      type: Boolean,
      default: false
    },
    allowRemove: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      pageSize: 10,
      paginatedList: []
    };
  },
  computed: {
    announcement: get("announcementModule/announcement"),
    hasSurvey: get('announcementModule/announcement@hasSurvey'),
    selectedUserProfile: sync("announcementModule/selectedUserProfile"),
    uncompletedUserProfile: get("announcementModule/uncompletedUserProfile"),
    showChat() {
      return this.announcement?.comunications?.MESSAGE;
    },
    typeCourse() {
      // [1] Teleformación [2] Presencial [3] Mixto [4] Aula Virtual
      return this.announcement?.course ? this.announcement.course.typeID : 1;
    },
    hideInPresential() {
      return !(this.typeCourse === 2) && this.announcement?.source === ANNOUNCEMENT_SOURCE.INTERN;
    },
    hideInTeleformation() {
      return !(this.typeCourse === 1) && this.announcement?.source === ANNOUNCEMENT_SOURCE.INTERN;
    },

    showConfirmation() {
      return this.announcement?.source === ANNOUNCEMENT_SOURCE.INTERN;
    },

    hasBonification(){
      return this.announcement?.hasBonification ?? false;
    },

    studentsFiltered() {
      if (this.query.length === 0) return this.studentList;
      const query = this.query.toLowerCase();
      return this.studentList.filter(
        (item) =>
          item.name.toLowerCase().includes(query) ||
          item.dni.toLowerCase().includes(query)
      );
    },
  },
  mounted() {
    this.paginatedList = this.studentList.slice(0, this.pageSize);
  },
  methods: {
    setUser(index) {
      this.$store.dispatch(
        "announcementModule/setUserSelected",
        this.paginatedList[index] || {}
      );
    },

    fillUserProfile(user) {
      this.selectedUserProfile = user;
    },
    
    openDeleteDialog(student, announcementId) {
      this.$emit('remove', {
        userId: student.id,
        groupId: student.groupId,
        announcementId: announcementId
      })
    },
  },
};
</script>

 <style scoped lang="scss"> 
.studentsTable {
  .avatarContainer {
    width: 75px;
    text-align: right;
  }
  .userAvatar {
    width: 2rem;
    height: 2rem;
  }
  .fa-exclamation {
    margin-right: 0.2rem;
    color: var(--color-dashboard-4);
  }
  .comunicationIcons {
    gap: 1.2rem;
  }

  .inactive {
    border-bottom: solid 3px var(--color-tertiary);
  }
}
</style>
