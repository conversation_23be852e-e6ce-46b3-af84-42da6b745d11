<?php

declare(strict_types=1);

namespace App\Tests\V2\Application\QueryHandler;

use App\Tests\V2\Mother\VirtualMeeting\VirtualMeetingMother;
use App\V2\Application\Query\GetVirtualMeetings;
use App\V2\Application\QueryHandler\GetVirtualMeetingsQueryHandler;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\VirtualMeeting\PaginatedVirtualMeetingCollection;
use App\V2\Domain\VirtualMeeting\VirtualMeetingCollection;
use App\V2\Domain\VirtualMeeting\VirtualMeetingCriteria;
use App\V2\Domain\VirtualMeeting\VirtualMeetingRepository;
use PHPUnit\Framework\MockObject\Exception;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;

class GetVirtualMeetingsQueryHandlerTest extends TestCase
{
    private VirtualMeetingRepository&MockObject $virtualMeetingRepository;
    private GetVirtualMeetingsQueryHandler $handler;

    /**
     * @throws Exception
     */
    protected function setUp(): void
    {
        $this->virtualMeetingRepository = $this->createMock(VirtualMeetingRepository::class);
        $this->handler = new GetVirtualMeetingsQueryHandler($this->virtualMeetingRepository);
    }

    /**
     * @throws InfrastructureException
     * @throws \DateMalformedStringException
     */
    public function testSuccessfullyHandler(): void
    {
        // Arrange
        $criteria = VirtualMeetingCriteria::createEmpty();
        $query = new GetVirtualMeetings($criteria);

        $vm1 = VirtualMeetingMother::create();
        $vm2 = VirtualMeetingMother::create();
        $vm3 = VirtualMeetingMother::create();

        $collection = new VirtualMeetingCollection([$vm1, $vm2, $vm3]);
        $totalItems = 10;

        // Expectations
        $this->virtualMeetingRepository
            ->expects($this->once())
            ->method('findBy')
            ->with($criteria)
            ->willReturn($collection);

        $this->virtualMeetingRepository
            ->expects($this->once())
            ->method('countBy')
            ->with($criteria)
            ->willReturn($totalItems);

        // Act
        $result = $this->handler->handle($query);

        // Assert
        $this->assertInstanceOf(PaginatedVirtualMeetingCollection::class, $result);
        $this->assertSame($collection, $result->getCollection());
        $this->assertCount(3, $result->getCollection()->all());
        $this->assertSame($totalItems, $result->getTotalItems());
    }

    /**
     * @throws InfrastructureException
     */
    public function testHandlerWithEmptyCollection(): void
    {
        // Arrange
        $criteria = VirtualMeetingCriteria::createEmpty();
        $query = new GetVirtualMeetings($criteria);

        $emptyCollection = new VirtualMeetingCollection([]);

        // Expectations
        $this->virtualMeetingRepository
            ->expects($this->once())
            ->method('findBy')
            ->with($criteria)
            ->willReturn($emptyCollection);

        $this->virtualMeetingRepository
            ->expects($this->once())
            ->method('countBy')
            ->with($criteria)
            ->willReturn(0);

        // Act
        $result = $this->handler->handle($query);

        // Assert
        $this->assertInstanceOf(PaginatedVirtualMeetingCollection::class, $result);
        $this->assertSame($emptyCollection, $result->getCollection());
        $this->assertCount(0, $result->getCollection()->all());
        $this->assertTrue($result->getCollection()->isEmpty());
        $this->assertSame(0, $result->getTotalItems());
    }
}
