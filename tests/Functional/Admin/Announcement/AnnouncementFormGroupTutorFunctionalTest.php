<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Announcement;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminAnnouncementEndpoints;
use App\Tests\Functional\SettingsConstants;
use App\Tests\Functional\V2\Fixtures\AnnouncementManagerFixtureTrait;
use Symfony\Component\HttpFoundation\Response;

/**
 * The test is only testing access constraints. No internal business logic has been tested.
 * For internal business logic, is recommended to migrate to V2 CQRS structure to enforce validations.
 */
class AnnouncementFormGroupTutorFunctionalTest extends FunctionalTestCase
{
    use AnnouncementManagerFixtureTrait;

    private User $manager1;
    private User $manager2;

    private Announcement $announcement1;
    private AnnouncementGroup $announcementGroup1;
    private Announcement $announcement2;
    private AnnouncementGroup $announcementGroup2;

    protected function setUp(): void
    {
        parent::setUp();

        $this->manager1 = $this->createAndGetUser(
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>',
        );

        $this->manager2 = $this->createAndGetUser(
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>',
        );

        $course1 = $this->createAndGetCourse(name: 'Course 1', code: 'COURSE-1');
        $course2 = $this->createAndGetCourse(name: 'Course 2', code: 'COURSE-2');

        $this->announcement1 = $this->createAndGetAnnouncement(course: $course1, createdBy: $this->manager1);
        $this->announcement2 = $this->createAndGetAnnouncement(course: $course2, createdBy: $this->manager2);
        $this->announcementGroup1 = $this->createAndGetAnnouncementGroup(
            announcement: $this->announcement1,
        );
        $this->announcementGroup2 = $this->createAndGetAnnouncementGroup(
            announcement: $this->announcement2,
        );
    }

    public function testNoId(): void
    {
        $token = $this->loginAndGetToken(email: '<EMAIL>');

        // Test announcement created by another user
        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::legacyAnnouncementFormGroupTutorEndpoint(
                announcementGroupId: 0
            ),
            bearerToken: $token,
        );
        $this->assertEquals(Response::HTTP_ACCEPTED, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $content);
        $this->assertArrayHasKey('error', $content);
        $this->assertTrue($content['error']);
        $this->assertEquals('`id` is required', $content['data']);
    }

    public function testAnnouncementNotFound(): void
    {
        $token = $this->loginAndGetToken(email: '<EMAIL>');

        // Test announcement created by another user
        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::legacyAnnouncementFormGroupTutorEndpoint(
                announcementGroupId: 999
            ),
            bearerToken: $token,
        );
        $this->assertEquals(Response::HTTP_ACCEPTED, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $content);
        $this->assertArrayHasKey('error', $content);
        $this->assertTrue($content['error']);
        $this->assertEquals('Announcement group not found', $content['data']);
    }

    public function testAsManagerWithSharingDisabled(): void
    {
        $this->setValueAndGetSetting(
            code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING,
            value: 'false',
        );

        $token = $this->loginAndGetToken(email: '<EMAIL>');

        // Test announcement created by another user
        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::legacyAnnouncementFormGroupTutorEndpoint(
                announcementGroupId: $this->announcementGroup1->getId()
            ),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertStringContainsString(
            'User <EMAIL> is not authorized to perform this action for announcement Course ',
            $content['message']
        );

        // Test announcement created by the user
        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::legacyAnnouncementFormGroupTutorEndpoint(
                announcementGroupId: $this->announcementGroup2->getId()
            ),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_ACCEPTED, $response->getStatusCode());
    }

    public function testAsManagerWithSharingEnabled(): void
    {
        $this->setValueAndGetSetting(
            code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING,
            value: 'true',
        );

        $this->setAndGetAnnouncementManager(
            userId: $this->manager2->getId(),
            announcementId: $this->announcement1->getId(),
        );

        $token = $this->loginAndGetToken(email: '<EMAIL>');
        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::legacyAnnouncementFormGroupTutorEndpoint(
                announcementGroupId: $this->announcementGroup1->getId()
            ),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_ACCEPTED, $response->getStatusCode());
    }

    public function testAsAdmin(): void
    {
        $token = $this->loginAndGetToken();
        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::legacyAnnouncementFormGroupTutorEndpoint(
                announcementGroupId: $this->announcementGroup2->getId()
            ),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_ACCEPTED, $response->getStatusCode());
    }

    protected function tearDown(): void
    {
        $this->setValueAndGetSetting(
            code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING,
            value: 'false',
        );
        $this->truncateEntities([
            AnnouncementGroup::class,
            Announcement::class,
            Course::class,
            CourseCategory::class,
        ]);

        $this->hardDeleteUsersByIds([
            $this->manager1->getId(),
            $this->manager2->getId(),
        ]);
        parent::tearDown();
    }
}
