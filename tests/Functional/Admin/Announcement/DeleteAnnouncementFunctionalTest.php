<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Announcement;

use App\Entity\Announcement;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminAnnouncementEndpoints;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class DeleteAnnouncementFunctionalTest extends FunctionalTestCase
{
    private User $manager1;
    private User $manager2;
    private Course $course1;
    private Course $course2;

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->manager1 = $this->createAndGetUser(
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>',
        );

        $this->manager2 = $this->createAndGetUser(
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>',
        );

        $this->course1 = $this->createAndGetCourse(name: 'Course 1', code: 'COURSE-1');
        $this->course2 = $this->createAndGetCourse(name: 'Course 2', code: 'COURSE-2');
    }

    public function testDeleteAnnouncementAsAdmin(): void
    {
        $configurationAnnouncement = $this->createAndGetAnnouncement(
            course: $this->course1,
            status: Announcement::STATUS_CONFIGURATION,
            createdBy: $this->manager1,
        );

        $activeAnnouncement = $this->createAndGetAnnouncement(
            course: $this->course1,
            status: Announcement::STATUS_ACTIVE,
            createdBy: $this->manager1,
        );

        $token = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminAnnouncementEndpoints::deleteAnnouncementEndpoint($configurationAnnouncement->getId()),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $configurationAnnouncement = $this->getAnnouncementRepository()->find($configurationAnnouncement->getId());
        $this->assertEquals(Announcement::STATUS_ARCHIVED, $configurationAnnouncement->getStatus());

        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminAnnouncementEndpoints::deleteAnnouncementEndpoint($activeAnnouncement->getId()),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertStringContainsString(
            'Status ARCHIVED cannot be set to announcement Course 1',
            $content['message']
        );
    }

    public function testDeleteAnnouncementAsManager(): void
    {
        $configurationAnnouncement1 = $this->createAndGetAnnouncement(
            course: $this->course1,
            status: Announcement::STATUS_CONFIGURATION,
            createdBy: $this->manager1,
        );

        $configurationAnnouncement2Finished = $this->createAndGetAnnouncement(
            course: $this->course1,
            status: Announcement::STATUS_FINISHED,
            createdBy: $this->manager2,
        );

        $configurationAnnouncement2Active = $this->createAndGetAnnouncement(
            course: $this->course1,
            status: Announcement::STATUS_ACTIVE,
            createdBy: $this->manager2,
        );

        $token = $this->loginAndGetToken(email: '<EMAIL>');

        // Try to delete the announcement created by other user
        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminAnnouncementEndpoints::deleteAnnouncementEndpoint($configurationAnnouncement1->getId()),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertStringContainsString(
            'User <EMAIL> is not authorized to delete the announcement Course 1',
            $content['message']
        );

        $result = $this->getAnnouncementRepository()->find($configurationAnnouncement1->getId());
        $this->assertEquals(Announcement::STATUS_CONFIGURATION, $result->getStatus());

        // Try to delete the finished announcement created by the user
        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminAnnouncementEndpoints::deleteAnnouncementEndpoint($configurationAnnouncement2Finished->getId()),
            bearerToken: $token,
        );
        $this->assertEquals(Response::HTTP_NO_CONTENT, $response->getStatusCode());

        $result = $this->getAnnouncementRepository()->find($configurationAnnouncement2Finished->getId());
        $this->assertEquals(Announcement::STATUS_ARCHIVED, $result->getStatus());

        // Try to delete an active announcement created by the user
        $response = $this->makeRequest(
            method: 'DELETE',
            uri: AdminAnnouncementEndpoints::deleteAnnouncementEndpoint($configurationAnnouncement2Active->getId()),
            bearerToken: $token,
        );
        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertStringContainsString(
            'Status ARCHIVED cannot be set to announcement Course 1',
            $content['message']
        );

        $result = $this->getAnnouncementRepository()->find($configurationAnnouncement2Active->getId());
        $this->assertEquals(Announcement::STATUS_ACTIVE, $result->getStatus());
    }

    /**
     * @throws Exception
     * @throws MappingException
     */
    protected function tearDown(): void
    {
        $this->truncateEntities([
            Announcement::class,
            Course::class,
            CourseCategory::class,
        ]);

        $this->hardDeleteUsersByIds([
            $this->manager1->getId(),
            $this->manager2->getId(),
        ]);

        parent::tearDown();
    }
}
