<?php

declare(strict_types=1);

namespace App\Tests\Functional\Admin\Announcement;

use App\Entity\Announcement;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminAnnouncementEndpoints;
use App\Tests\Functional\SettingsConstants;
use App\Tests\Functional\V2\Fixtures\AnnouncementManagerFixtureTrait;
use App\Tests\Functional\V2\Fixtures\CourseManagerFixtureTrait;
use App\V2\Domain\Shared\Id\Id;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class AnnouncementPostCloneAnnouncementFunctionalTest extends FunctionalTestCase
{
    use AnnouncementManagerFixtureTrait;
    use CourseManagerFixtureTrait;

    private User $manager1;
    private User $manager2;

    private Course $course1;
    private Course $course2;
    private Announcement $announcement1;
    private Announcement $announcement2;

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    protected function setUp(): void
    {
        parent::setUp();

        $this->manager1 = $this->createAndGetUser(
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>',
        );

        $this->manager2 = $this->createAndGetUser(
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>',
        );

        $this->course1 = $this->createAndGetCourse(name: 'Course 1', code: 'COURSE-1');
        $this->course2 = $this->createAndGetCourse(name: 'Course 2', code: 'COURSE-2');

        $this->announcement1 = $this->createAndGetAnnouncement(course: $this->course1, createdBy: $this->manager1);
        $this->announcement2 = $this->createAndGetAnnouncement(course: $this->course2, createdBy: $this->manager2);
    }

    public function testCloneAsAdmin(): void
    {
        $token = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::legacyCloneAnnouncementEndpoint($this->announcement1->getId()),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $content);
        $data = $content['data'];
        $this->assertArrayHasKey('idAnnouncement', $data);
    }

    public function testCloneAsManagerWithSharingDisabledAndNoCoursesAssigned(): void
    {
        $this->setValueAndGetSetting(
            code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING,
            value: 'false',
        );
        $token = $this->loginAndGetToken(
            email: '<EMAIL>'
        );
        // Try to clone other announcement
        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::legacyCloneAnnouncementEndpoint($this->announcement2->getId()),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertStringContainsString(
            'User <EMAIL> is not authorized to perform this action for announcement Course 2',
            $content['message']
        );

        // Try to clone its own created announcement
        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::legacyCloneAnnouncementEndpoint($this->announcement1->getId()),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $content);
        $data = $content['data'];
        $this->assertArrayHasKey('idAnnouncement', $data);
    }

    public function testCloneAsManagerWithSharingEnabledAndNoCoursesAssigned(): void
    {
        $this->setValueAndGetSetting(
            code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING,
            value: 'true',
        );
        $token = $this->loginAndGetToken(
            email: '<EMAIL>'
        );
        // Try to clone other announcement
        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::legacyCloneAnnouncementEndpoint($this->announcement2->getId()),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertStringContainsString(
            'User <EMAIL> is not authorized to perform this action for announcement Course 2',
            $content['message']
        );

        // Share the announcement
        $this->setAndGetAnnouncementManager(
            userId: $this->manager1->getId(),
            announcementId: $this->announcement2->getId(),
        );

        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::legacyCloneAnnouncementEndpoint($this->announcement2->getId()),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $content);
        $data = $content['data'];
        $this->assertArrayHasKey('idAnnouncement', $data);
    }

    public function testCloneAsManagerWithCoursesShared(): void
    {
        $this->setValueAndGetSetting(
            code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING,
            value: 'false',
        );
        $this->setAndGetCourseManagerInRepository(
            userId: new Id($this->manager1->getId()),
            courseId: new Id($this->course2->getId()),
        );
        $token = $this->loginAndGetToken(
            email: '<EMAIL>'
        );

        // Try to clone its own announcement, with the course not allowed.
        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::legacyCloneAnnouncementEndpoint($this->announcement1->getId()),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertStringContainsString(
            'User <EMAIL> does not have permission to create an announcement for the course: Course 1',
            $content['message']
        );

        // Assign the course
        $this->setAndGetCourseManagerInRepository(
            userId: new Id($this->manager1->getId()),
            courseId: new Id($this->course1->getId()),
        );
        $response = $this->makeRequest(
            method: 'POST',
            uri: AdminAnnouncementEndpoints::legacyCloneAnnouncementEndpoint($this->announcement1->getId()),
            bearerToken: $token,
        );

        $this->assertEquals(Response::HTTP_OK, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('data', $content);
        $data = $content['data'];
        $this->assertArrayHasKey('idAnnouncement', $data);
    }

    /**
     * @throws MappingException
     * @throws ORMException
     * @throws NotSupported
     * @throws Exception
     */
    protected function tearDown(): void
    {
        $this->setValueAndGetSetting(
            code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING,
            value: 'false',
        );
        $this->truncateEntities([
            Announcement::class,
            Course::class,
            CourseCategory::class,
        ]);

        $this->hardDeleteUsersByIds([
            $this->manager1->getId(),
            $this->manager2->getId(),
        ]);
        parent::tearDown();
    }
}
