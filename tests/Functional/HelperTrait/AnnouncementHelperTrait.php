<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait;

use App\Entity\Announcement;
use App\Entity\AnnouncementConfiguration;
use App\Entity\AnnouncementConfigurationType;
use App\Entity\AnnouncementDidaticGuide;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementGroupSession;
use App\Entity\AnnouncementManager;
use App\Entity\AnnouncementTutor;
use App\Entity\AnnouncementUser;
use App\Entity\AnnouncementUserDigitalSignature;
use App\Entity\ClassroomvirtualUser;
use App\Entity\Course;
use App\Entity\User;
use App\Repository\AnnouncementRepository;
use App\Tests\Mother\Entity\AnnouncementMother;
use Doctrine\ORM\EntityRepository;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;

/**
 * Trait with methods to create, update, and delete Announcement.
 */
trait AnnouncementHelperTrait
{
    /**
     * @throws NotSupported
     */
    protected function getAnnouncementRepository(): AnnouncementRepository|EntityRepository
    {
        return $this->getEntityManager()
            ->getRepository(Announcement::class);
    }

    /**
     * Creates and persists an Announcement associated with a Course.
     *
     * @throws ORMException
     */
    protected function createAndGetAnnouncement(
        ?Course $course = null,
        ?\DateTimeImmutable $startAt = null,
        ?\DateTimeImmutable $finishAt = null,
        string $status = Announcement::STATUS_ACTIVE,
        bool $subsidized = false,
        string $code = 'DemoAnnouncementCode-1',
        float $totalHours = 10,
        ?string $timezone = null,
        ?User $createdBy = null,
        ?\DateTimeImmutable $notifiedAt = null,
    ): Announcement {
        $em = $this->getEntityManager();

        if (!$course) {
            $course = $this->createAndGetCourse();
        }

        $announcement = AnnouncementMother::create(
            course: $course,
            startAt: $startAt,
            finishAt: $finishAt,
            status: $status,
            subsidized: $subsidized,
            code: $code,
            totalHours: $totalHours,
            timezone: $timezone,
            createdBy: $createdBy,
        );

        if ($notifiedAt) {
            $announcement->setNotifiedAt($notifiedAt);
        }

        $em->persist($announcement);
        $em->flush();

        return $announcement;
    }

    /**
     * Creates and persists an AnnouncementUser associating the Announcement and the User.
     *
     * @throws ORMException
     */
    protected function createAndGetAnnouncementUser(
        Announcement $announcement,
        ?User $user = null,
        ?bool $isApproved = null,
        ?\DateTimeImmutable $dateApproved = null,
        ?AnnouncementGroup $announcementGroup = null,
        bool $isReadDidacticGuide = false,
    ): AnnouncementUser {
        $em = $this->getEntityManager();

        $announcementUser = new AnnouncementUser();
        $announcementUser->setAnnouncement($announcement);
        $announcementUser->setUser($user ?? $this->getDefaultUser());
        $announcementUser->setAproved($isApproved);
        $announcementUser->setDateApproved($dateApproved);
        $announcementUser->setAnnouncementGroup($announcementGroup);
        $announcementUser->setReadDidacticGuide($isReadDidacticGuide);

        $em->persist($announcementUser);
        $em->flush();

        return $announcementUser;
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetAnnouncementGroup(
        Announcement $announcement,
        ?\DateTimeImmutable $createdAt = null,
        ?\DateTimeImmutable $updatedAt = null,
        bool $withSessions = true,
    ): AnnouncementGroup {
        $em = $this->getEntityManager();

        $announcementGroup = new AnnouncementGroup();
        $announcementGroup->setAnnouncement($announcement);
        $announcementGroup->setCreatedAt($createdAt ?? new \DateTimeImmutable('today'));
        $announcementGroup->setUpdatedAt($updatedAt ?? new \DateTimeImmutable('today'));
        $announcementGroup->setCode('DemoAnnouncementGroupCode-1');

        $em->persist($announcementGroup);
        $em->flush();

        if ($withSessions) {
            $this->createAndGetGroupSessions($announcementGroup);
        }

        return $announcementGroup;
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    private function createAndGetGroupSessions(AnnouncementGroup $announcementGroup): AnnouncementGroupSession
    {
        $announcementGroupSession = new AnnouncementGroupSession();
        $announcementGroupSession->setAnnouncementGroup($announcementGroup);
        $announcementGroupSession->setStartAt(new \DateTimeImmutable('today'));
        $announcementGroupSession->setFinishAt(new \DateTimeImmutable('tomorrow'));

        $em = $this->getEntityManager();
        $em->persist($announcementGroupSession);
        $em->flush();

        return $announcementGroupSession;
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetAnnouncementTutor(
        Announcement $announcement,
        AnnouncementGroup $announcementGroup,
        ?User $tutor = null
    ): AnnouncementTutor {
        $em = $this->getEntityManager();

        $announcementTutor = new AnnouncementTutor();
        $announcementTutor->setAnnouncement($announcement);
        $announcementTutor->setAnnouncementGroup($announcementGroup);
        $announcementTutor->setTutor($tutor ?? $this->getDefaultUser());

        $em->persist($announcementTutor);
        $em->flush();

        return $announcementTutor;
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetAnnouncementDidacticGuide(
        Announcement $announcement,
        $createdAt = null,
        $updatedAt = null,
        $filename = 'demoDidacticGuide.pdf',
        $originalName = 'Demo didactic guide'
    ): AnnouncementDidaticGuide {
        $announcementDidacticGuide = new AnnouncementDidaticGuide();
        $announcementDidacticGuide->setAnnouncement($announcement);
        $announcementDidacticGuide->setCreatedAt($createdAt ?? new \DateTimeImmutable());
        $announcementDidacticGuide->setUpdatedAt($updatedAt ?? new \DateTimeImmutable());
        $announcementDidacticGuide->setFilename($filename);
        $announcementDidacticGuide->setOriginalName($originalName);

        $em = $this->getEntityManager();
        $em->persist($announcementDidacticGuide);
        $em->flush();

        return $announcementDidacticGuide;
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetAnnouncementConfiguration(Announcement $announcement): AnnouncementConfiguration
    {
        $announcementConfiguration = new AnnouncementConfiguration();
        $announcementConfigurationType = $this->getEntityManager()->getRepository(AnnouncementConfigurationType::class)->findOneBy(['id' => 1]);

        $announcementConfiguration->setAnnouncement($announcement);
        $announcementConfiguration->setConfiguration($announcementConfigurationType);
        $announcementConfiguration->setCreatedAt($createdAt ?? new \DateTimeImmutable());
        $announcementConfiguration->setUpdatedAt($updatedAt ?? new \DateTimeImmutable());

        $em = $this->getEntityManager();
        $em->persist($announcementConfiguration);
        $em->flush();

        return $announcementConfiguration;
    }

    protected function createAndGetAnnouncementUserDigitalSignature(
        AnnouncementUser $announcementUser,
    ): AnnouncementUserDigitalSignature {
        $signature = new AnnouncementUserDigitalSignature();

        $signature->setAnnouncementUser($announcementUser);

        $em = $this->getEntityManager();
        $em->persist($signature);
        $em->flush();

        return $signature;
    }

    protected function createAndGetAnnouncementClassroomVirtualUser(
        AnnouncementUser $announcementUser,
    ): ClassroomvirtualUser {
        $virtualUser = new ClassroomvirtualUser();

        $virtualUser->setAnnouncementuser($announcementUser);

        $em = $this->getEntityManager();
        $em->persist($virtualUser);
        $em->flush();

        return $virtualUser;
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    protected function createAndGetAnnouncementManager(Announcement $announcement, User $user): AnnouncementManager
    {
        $announcementManager = new AnnouncementManager();
        $announcementManager->setAnnouncement($announcement);
        $announcementManager->setManager($user);

        $em = $this->getEntityManager();
        $em->persist($announcementManager);
        $em->flush();

        return $announcementManager;
    }

    /**
     * Deletes an Announcement from the database.
     *
     * @throws ORMException
     */
    protected function removeAnnouncement(Announcement $announcement): void
    {
        $em = $this->getEntityManager();
        $em->remove($announcement);
        $em->flush();
    }

    /**
     * Deletes an AnnouncementUser from the database.
     *
     * @throws ORMException
     */
    protected function removeAnnouncementUser(AnnouncementUser $announcementUser): void
    {
        $em = $this->getEntityManager();
        $em->remove($announcementUser);
        $em->flush();
    }
}
