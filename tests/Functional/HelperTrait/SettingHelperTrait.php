<?php

declare(strict_types=1);

namespace App\Tests\Functional\HelperTrait;

use App\Entity\Setting;
use App\Entity\SettingGroup;
use App\Tests\Mother\SettingMother;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;

trait SettingHelperTrait
{
    /**
     * @throws OptimisticLockException
     * @throws NotSupported
     * @throws ORMException
     */
    protected function createAndGetSetting(
        ?int $id = null,
        string $code = 'app.setting.default',
        string $name = 'Default Setting',
        ?string $description = 'Description',
        int $sort = 999,
        array $options = [],
        string $type = 'bool',
        string $value = 'false',
        ?SettingGroup $settingGroup = null
    ): Setting {
        $em = $this->getEntityManager();
        $settingRepository = $em->getRepository(Setting::class);

        if (null !== $id) {
            $setting = $settingRepository->find($id);
            if (null !== $setting) {
                return $setting;
            }
        }

        $setting = $settingRepository->findOneBy([
            'code' => $code,
        ]);

        if (null !== $setting) {
            return $setting;
        }

        $setting = SettingMother::create(
            id: $id,
            code: $code,
            name: $name,
            description: $description,
            sort: $sort,
            options: $options,
            type: $type,
            value: $value,
            settingGroup: $settingGroup
        );

        $originalMetadata = $this->setCustomIdToEntity($setting);
        $em->persist($setting);
        $em->flush();
        $this->restoreEntityMetadata($setting, $originalMetadata);

        return $setting;
    }

    /**
     * @throws NotSupported
     * @throws ORMException
     */
    protected function setValueAndGetSetting(string $code, $value): Setting
    {
        $this->updateSettingValue(value: $value, code: $code);

        $setting = $this->getEntityManager()
            ->getRepository(Setting::class)
            ->findOneBy(['code' => $code]);

        if (!$setting) {
            $this->fail(\sprintf('Setting "%s" not found.', $code));
        }

        return $setting;
    }
}
