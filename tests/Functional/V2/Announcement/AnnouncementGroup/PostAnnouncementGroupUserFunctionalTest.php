<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Announcement\AnnouncementGroup;

use App\Entity\Announcement;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementUser;
use App\Entity\Course;
use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminAnnouncementGroupEndpoints;
use App\Tests\Functional\SettingsConstants;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use PHPUnit\Framework\Attributes\DataProvider;
use Symfony\Component\HttpFoundation\Response;

class PostAnnouncementGroupUserFunctionalTest extends FunctionalTestCase
{
    private array $userIds = [];

    /**
     * @throws Exception
     * @throws MappingException
     */
    protected function tearDown(): void
    {
        $this->setValueAndGetSetting(
            code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING,
            value: 'false',
        );
        // Truncate entities created during tests
        $this->truncateEntities([
            AnnouncementUser::class,
            AnnouncementGroup::class,
            Announcement::class,
            Course::class,
        ]);

        // Hard delete users created for the test
        if (!empty($this->userIds)) {
            $this->hardDeleteUsersByIds($this->userIds);
        }

        parent::tearDown();
    }

    public function testPostAnnouncementGroupUserReturns401WhenNotAuthenticated(): void
    {
        $endpoint = AdminAnnouncementGroupEndpoints::announcementGroupUserEndpoint(
            announcementId: 1,
            groupId: 1,
            userId: 1
        );

        $this->makeRequest('POST', $endpoint);

        $this->assertResponseStatusCodeSame(Response::HTTP_UNAUTHORIZED);
    }

    #[DataProvider('notFoundEntitiesProvider')]
    public function testPostAnnouncementGroupUserReturns404WhenEntityNotFound(
        int $announcementId,
        int $groupId,
        int $userId
    ): void {
        $endpoint = AdminAnnouncementGroupEndpoints::announcementGroupUserEndpoint(
            announcementId: $announcementId,
            groupId: $groupId,
            userId: $userId
        );

        $token = $this->loginAndGetToken();
        $response = $this->makeRequest(
            method: 'POST',
            uri: $endpoint,
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());
    }

    public static function notFoundEntitiesProvider(): array
    {
        return [
            'announcement not found' => [999999, 1, 1],
            'group not found' => [1, 999999, 1],
            'user not found' => [1, 1, 999999],
        ];
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testPostAnnouncementGroupUserReturns422WhenUserAlreadyInAnnouncement(): void
    {
        // Create test data
        $course = $this->createAndGetCourse();
        $announcement = $this->createAndGetAnnouncement(course: $course);
        $group = $this->createAndGetAnnouncementGroup(announcement: $announcement);

        $targetUser = $this->createAndGetUser(
            firstName: 'Target',
            lastName: 'User',
            email: '<EMAIL>',
            roles: [User::ROLE_USER]
        );
        $this->userIds[] = $targetUser->getId();

        // Pre-create AnnouncementUser to simulate user already in announcement
        $this->createAndGetAnnouncementUser(
            announcement: $announcement,
            user: $targetUser,
            announcementGroup: $group
        );

        $endpoint = AdminAnnouncementGroupEndpoints::announcementGroupUserEndpoint(
            announcementId: $announcement->getId(),
            groupId: $group->getId(),
            userId: $targetUser->getId()
        );

        $token = $this->loginAndGetToken();
        $response = $this->makeRequest(
            method: 'POST',
            uri: $endpoint,
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('User already belongs to this announcement', $responseData['message']);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testPostAnnouncementGroupUserReturns422WhenMaxGroupSizeReached(): void
    {
        // Create test data with limited group size
        $course = $this->createAndGetCourse();
        $announcement = $this->createAndGetAnnouncement(course: $course);

        // Set max group size to 1
        $announcement->setUsersPerGroup(1);
        $this->getEntityManager()->flush();

        $group = $this->createAndGetAnnouncementGroup(announcement: $announcement);

        // Create first user and add to group (fills the group)
        $firstUser = $this->createAndGetUser(
            firstName: 'First',
            lastName: 'User',
            roles: [User::ROLE_USER],
            email: '<EMAIL>'
        );
        $this->userIds[] = $firstUser->getId();

        $this->createAndGetAnnouncementUser(
            announcement: $announcement,
            user: $firstUser,
            announcementGroup: $group
        );

        // Create second user to try to add (should fail due to group size limit)
        $secondUser = $this->createAndGetUser(
            firstName: 'Second',
            lastName: 'User',
            roles: [User::ROLE_USER],
            email: '<EMAIL>'
        );
        $this->userIds[] = $secondUser->getId();

        $endpoint = AdminAnnouncementGroupEndpoints::announcementGroupUserEndpoint(
            announcementId: $announcement->getId(),
            groupId: $group->getId(),
            userId: $secondUser->getId()
        );

        $token = $this->loginAndGetToken();
        $response = $this->makeRequest(
            method: 'POST',
            uri: $endpoint,
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertEquals('Maximum group size reached', $responseData['message']);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testPostAnnouncementGroupUserReturns403WhenManagerNotAuthorized(): void
    {
        // Enable announcement manager sharing for this test
        $this->setValueAndGetSetting(
            code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING,
            value: 'true',
        );

        // Create announcement owner (different from request user)
        $announcementOwner = $this->createAndGetUser(
            firstName: 'Owner',
            lastName: 'Manager',
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>'
        );
        $this->userIds[] = $announcementOwner->getId();

        // Create test data
        $course = $this->createAndGetCourse();
        $announcement = $this->createAndGetAnnouncement(
            course: $course,
            createdBy: $announcementOwner
        );
        $group = $this->createAndGetAnnouncementGroup(announcement: $announcement);

        // Create unauthorized manager (not owner, not shared)
        $unauthorizedManager = $this->createAndGetUser(
            firstName: 'Unauthorized',
            lastName: 'Manager',
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>'
        );
        $this->userIds[] = $unauthorizedManager->getId();

        // Create target user that the unauthorized manager CAN manage
        // By creating it with the manager as creator, the manager can definitely find it
        $targetUser = $this->createAndGetUser(
            firstName: 'Target',
            lastName: 'User',
            roles: [User::ROLE_USER],
            email: '<EMAIL>',
            createdBy: $unauthorizedManager // Created by the manager, so they can manage it
        );
        $this->userIds[] = $targetUser->getId();

        $endpoint = AdminAnnouncementGroupEndpoints::announcementGroupUserEndpoint(
            announcementId: $announcement->getId(),
            groupId: $group->getId(),
            userId: $targetUser->getId()
        );

        // Login as unauthorized manager
        $token = $this->loginAndGetTokenForUser($unauthorizedManager);
        $response = $this->makeRequest(
            method: 'POST',
            uri: $endpoint,
            bearerToken: $token
        );

        // Now the manager CAN find the user (because they created it)
        // But CANNOT access the announcement (because it's owned by another manager)
        // This should return 403 Forbidden
        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());

        $responseData = json_decode($response->getContent(), true);
        $this->assertStringContainsString(
            'is not authorized to perform this action for announcement',
            $responseData['message']
        );
        $this->assertStringContainsString(
            'unauthorized.manager.forbidden',
            $responseData['message']
        ); // Check email is included
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testPostAnnouncementGroupUserReturns201WhenSuccessful(): void
    {
        // Create test data
        $course = $this->createAndGetCourse();
        $announcement = $this->createAndGetAnnouncement(course: $course);
        $group = $this->createAndGetAnnouncementGroup(announcement: $announcement);

        $targetUser = $this->createAndGetUser(
            firstName: 'Target',
            lastName: 'User',
            roles: [User::ROLE_USER],
            email: '<EMAIL>'
        );
        $this->userIds[] = $targetUser->getId();

        $endpoint = AdminAnnouncementGroupEndpoints::announcementGroupUserEndpoint(
            announcementId: $announcement->getId(),
            groupId: $group->getId(),
            userId: $targetUser->getId()
        );

        $token = $this->loginAndGetToken(); // Default admin user
        $response = $this->makeRequest(
            method: 'POST',
            uri: $endpoint,
            bearerToken: $token
        );

        $this->assertEquals(Response::HTTP_CREATED, $response->getStatusCode());

        // The endpoint returns an empty JSON array on success
        $this->assertEquals('[]', $response->getContent());
    }
}
