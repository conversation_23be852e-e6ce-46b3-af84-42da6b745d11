<?php

declare(strict_types=1);

namespace App\Tests\Functional\V2\Announcement\Manager;

use App\Entity\Announcement;
use App\Entity\AnnouncementManager;
use App\Entity\Course;
use App\Entity\User;
use App\Tests\Functional\FunctionalTestCase;
use App\Tests\Functional\HelperTrait\Endpoints\Admin\AdminAnnouncementEndpoints;
use App\Tests\Functional\HelperTrait\SettingHelperTrait;
use App\Tests\Functional\SettingsConstants;
use App\Tests\Functional\V2\Fixtures\AnnouncementManagerFixtureTrait;
use Doctrine\DBAL\Exception;
use Doctrine\ORM\Exception\NotSupported;
use Doctrine\ORM\Exception\ORMException;
use Doctrine\ORM\OptimisticLockException;
use Doctrine\Persistence\Mapping\MappingException;
use Symfony\Component\HttpFoundation\Response;

class PutAnnouncementManagerFunctionalTest extends FunctionalTestCase
{
    use AnnouncementManagerFixtureTrait;
    use SettingHelperTrait;

    private array $usersIds = [];

    /**
     * @throws OptimisticLockException
     * @throws NotSupported
     * @throws ORMException
     */
    protected function setUp(): void
    {
        parent::setUp();
        $this->setValueAndGetSetting(
            code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING,
            value: 'true',
        );
    }

    public function testBadRequest(): void
    {
        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'PUT',
            uri: AdminAnnouncementEndpoints::announcementManagerEndpoint(announcementId: -1, userId: 1),
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Validation failed', $content['message']);
    }

    public function testBadRequestInvalidUserId(): void
    {
        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'PUT',
            uri: AdminAnnouncementEndpoints::announcementManagerEndpoint(announcementId: 1, userId: -1),
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_BAD_REQUEST, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Validation failed', $content['message']);
    }

    public function testAnnouncementNotFound()
    {
        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'PUT',
            uri: AdminAnnouncementEndpoints::announcementManagerEndpoint(announcementId: 9999, userId: 1),
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_NOT_FOUND, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Announcement not found', $content['message']);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testForbiddenRequest(): void
    {
        $this->setValueAndGetSetting(
            code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING,
            value: 'false',
        );

        $course = $this->createAndGetCourse();
        $announcement = $this->createAndGetAnnouncement(course: $course);

        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'PUT',
            uri: AdminAnnouncementEndpoints::announcementManagerEndpoint(
                announcementId: $announcement->getId(),
                userId: 1
            ),
            bearerToken: $userToken,
        );
        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('Announcement manager sharing is disabled', $content['message']);
    }

    public function testUserNotAuthorized()
    {
        $response = $this->makeRequest(
            method: 'PUT',
            uri: AdminAnnouncementEndpoints::announcementManagerEndpoint(announcementId: 1, userId: 1),
        );

        $this->assertEquals(Response::HTTP_UNAUTHORIZED, $response->getStatusCode());
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testUserNotFound()
    {
        $userToken = $this->loginAndGetToken();
        $course = $this->createAndGetCourse();
        $announcement = $this->createAndGetAnnouncement(course: $course);

        $response = $this->makeRequest(
            method: 'PUT',
            uri: AdminAnnouncementEndpoints::announcementManagerEndpoint(announcementId: $announcement->getId(), userId: 9999),
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('User not found', $content['message']);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testUserIsNotManager()
    {
        $userToken = $this->loginAndGetToken();
        $course = $this->createAndGetCourse();
        $announcement = $this->createAndGetAnnouncement(course: $course);

        $user = $this->createAndGetUser(
            firstName: 'Regular',
            lastName: 'User',
            roles: [User::ROLE_USER],
            email: '<EMAIL>'
        );

        $this->usersIds[] = $user->getId();

        $response = $this->makeRequest(
            method: 'PUT',
            uri: AdminAnnouncementEndpoints::announcementManagerEndpoint(announcementId: $announcement->getId(), userId: $user->getId()),
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('User is not a manager', $content['message']);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testManagerAlreadyExists()
    {
        $userToken = $this->loginAndGetToken();
        $course = $this->createAndGetCourse();
        $announcement = $this->createAndGetAnnouncement(course: $course);

        $user = $this->createAndGetUser(
            firstName: 'Manager',
            lastName: 'User',
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>'
        );

        $this->usersIds[] = $user->getId();

        $this->setAndGetAnnouncementManager(
            userId: $user->getId(),
            announcementId: $announcement->getId()
        );

        $response = $this->makeRequest(
            method: 'PUT',
            uri: AdminAnnouncementEndpoints::announcementManagerEndpoint(announcementId: $announcement->getId(), userId: $user->getId()),
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_UNPROCESSABLE_ENTITY, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);
        $this->assertEquals('User is already a manager of this announcement', $content['message']);
    }

    /**
     * @throws ORMException
     * @throws OptimisticLockException
     */
    public function testInvokeSuccessfulAddManagerAsAdmin(): void
    {
        $announcementOwner = $this->createAndGetUser(
            firstName: 'Announcement',
            lastName: 'Owner',
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>'
        );

        $this->usersIds[] = $announcementOwner->getId();

        $course = $this->createAndGetCourse();
        $announcement = $this->createAndGetAnnouncement(course: $course, createdBy: $announcementOwner);

        $manager = $this->createAndGetUser(
            firstName: 'Manager',
            lastName: 'User',
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>'
        );

        $this->usersIds[] = $manager->getId();

        $userToken = $this->loginAndGetToken();

        $response = $this->makeRequest(
            method: 'PUT',
            uri: AdminAnnouncementEndpoints::announcementManagerEndpoint(
                announcementId: $announcement->getId(),
                userId: $manager->getId()
            ),
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        $this->assertEmpty($response->getContent());
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testSuccessfulAddManagerAsOwner(): void
    {
        $announcementOwner = $this->createAndGetUser(
            firstName: 'Announcement',
            lastName: 'Owner',
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>'
        );

        $this->usersIds[] = $announcementOwner->getId();

        $course = $this->createAndGetCourse();
        $announcement = $this->createAndGetAnnouncement(course: $course, createdBy: $announcementOwner);

        $manager = $this->createAndGetUser(
            firstName: 'Manager',
            lastName: 'User',
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>'
        );

        $this->usersIds[] = $manager->getId();

        $userToken = $this->loginAndGetTokenForUser($announcementOwner);

        $response = $this->makeRequest(
            method: 'PUT',
            uri: AdminAnnouncementEndpoints::announcementManagerEndpoint(
                announcementId: $announcement->getId(),
                userId: $manager->getId()
            ),
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_NO_CONTENT, $response->getStatusCode());
        $this->assertEmpty($response->getContent());
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     */
    public function testForbiddenManagerNotOwner(): void
    {
        $announcementOwner = $this->createAndGetUser(
            firstName: 'Announcement',
            lastName: 'Owner',
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>'
        );

        $this->usersIds[] = $announcementOwner->getId();

        $course = $this->createAndGetCourse();
        $announcement = $this->createAndGetAnnouncement(course: $course, createdBy: $announcementOwner);

        $notOwnerManager = $this->createAndGetUser(
            firstName: 'Not Owner',
            lastName: 'Manager',
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>'
        );

        $this->usersIds[] = $notOwnerManager->getId();

        $managerToAdd = $this->createAndGetUser(
            firstName: 'Manager',
            lastName: 'User',
            roles: [User::ROLE_MANAGER],
            email: '<EMAIL>'
        );

        $this->usersIds[] = $managerToAdd->getId();

        $userToken = $this->loginAndGetTokenForUser($notOwnerManager);

        $response = $this->makeRequest(
            method: 'PUT',
            uri: AdminAnnouncementEndpoints::announcementManagerEndpoint(
                announcementId: $announcement->getId(),
                userId: $managerToAdd->getId()
            ),
            bearerToken: $userToken,
        );

        $this->assertEquals(Response::HTTP_FORBIDDEN, $response->getStatusCode());
        $content = json_decode($response->getContent(), true);
        $this->assertArrayHasKey('message', $content);

        $expectedUserEmail = $notOwnerManager->getEmail();
        $expectedCourseName = $course->getName();

        $this->assertStringContainsString($expectedUserEmail, $content['message']);
        $this->assertStringContainsString('is not authorized to perform this action', $content['message']);
        $this->assertStringContainsString('announcement', $content['message']);
        $this->assertStringContainsString($expectedCourseName, $content['message']);
    }

    /**
     * @throws OptimisticLockException
     * @throws ORMException
     * @throws MappingException
     * @throws NotSupported
     * @throws Exception
     */
    public function tearDown(): void
    {
        $this->setValueAndGetSetting(
            code: SettingsConstants::ANNOUNCEMENT_MANAGERS_SHARING,
            value: 'false',
        );

        $this->truncateEntities([
            AnnouncementManager::class,
            Announcement::class,
            Course::class,
        ]);

        if (!empty($this->usersIds)) {
            $this->hardDeleteUsersByIds($this->usersIds);
        }

        parent::tearDown();
    }
}
