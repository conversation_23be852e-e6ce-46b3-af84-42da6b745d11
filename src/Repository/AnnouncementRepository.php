<?php

declare(strict_types=1);

namespace App\Repository;

use App\Admin\Traits\FileValidationTrait;
use App\Admin\Traits\StatsQueryFiltersTrait;
use App\Entity\Announcement;
use App\Entity\AnnouncementAprovedCriteria;
use App\Entity\AnnouncementConfiguration;
use App\Entity\AnnouncementConfigurationType;
use App\Entity\AnnouncementCriteria;
use App\Entity\AnnouncementDidaticGuide;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementGroupSession;
use App\Entity\AnnouncementInspectorAccess;
use App\Entity\AnnouncementTemporalization;
use App\Entity\AnnouncementTutor;
use App\Entity\AnnouncementUser;
use App\Entity\Chapter;
use App\Entity\ChatChannel;
use App\Entity\ChatServer;
use App\Entity\Course;
use App\Entity\MaterialCourse;
use App\Entity\User;
use App\Entity\UserToken;
use App\Enum\MaterialsCourseEnum;
use App\Enum\TypeSession;
use App\Service\Annoucement\Admin\AnnouncementConfigurationsService;
use App\Service\Announcement\AnnouncementExtraService;
use App\Service\SettingsService;
use App\Utils\TimeZoneConverter\TimeZoneConverter;
use Doctrine\Bundle\DoctrineBundle\Repository\ServiceEntityRepository;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\NonUniqueResultException;
use Doctrine\ORM\NoResultException;
use Doctrine\Persistence\ManagerRegistry;
use Psr\Log\LoggerInterface;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * @method Announcement|null find($id, $lockMode = null, $lockVersion = null)
 * @method Announcement|null findOneBy(array $criteria, array $orderBy = null)
 * @method Announcement[]    findAll()
 * @method Announcement[]    findBy(array $criteria, array $orderBy = null, $limit = null, $offset = null)
 */
class AnnouncementRepository extends ServiceEntityRepository
{
    use FileValidationTrait;
    use StatsQueryFiltersTrait;

    private LoggerInterface $logger;
    private SettingsService $settings;
    private AnnouncementConfigurationsService $announcementConfigurationsService;
    protected TranslatorInterface $translator;
    private AnnouncementExtraService $announcementExtraService;
    private EntityManagerInterface $em;

    private array $allowedStatuses = [
        Announcement::STATUS_CONFIGURATION,
        Announcement::STATUS_ACTIVE,
        Announcement::STATUS_IN_PROGRESS,
        Announcement::STATUS_FINISHED,
        Announcement::STATUS_INACTIVE,
        Announcement::STATUS_ARCHIVED,
    ];

    // Participant code parsing constants
    private const int MIN_CODE_PARTS = 3;
    private const string CODE_SEPARATOR = '-';

    public function __construct(ManagerRegistry $registry, LoggerInterface $logger, SettingsService $settings, AnnouncementConfigurationsService $announcementConfigurationsService, TranslatorInterface $translator, EntityManagerInterface $entityManager, AnnouncementExtraService $announcementExtraService)
    {
        parent::__construct($registry, Announcement::class);
        $this->logger = $logger;
        $this->settings = $settings;
        $this->announcementConfigurationsService = $announcementConfigurationsService;
        $this->translator = $translator;
        $this->announcementExtraService = $announcementExtraService;
        $this->em = $entityManager;
    }

    public function setFinished(Announcement $announcement): bool
    {
        if (!Announcement::canChangeStatus($announcement, Announcement::STATUS_FINISHED)) {
            return false;
        }
        $announcement->setStatus(Announcement::STATUS_FINISHED);
        $this->_em->persist($announcement);
        $this->_em->flush();

        return true;
    }

    public function findByCourseAndUser(Course $course, User $user, bool $started = false)
    {
        $qb = $this->createQueryBuilder('a')
            ->innerJoin('a.called', 'c')
            ->andWhere('a.course = :course')
            ->andWhere('c.user = :user')
            ->andWhere($this->createQueryBuilder('a')->expr()->gte('a.finishAt', 'CURRENT_TIMESTAMP()'))
            ->setParameter('course', $course)
            ->setParameter('user', $user);

        $now = new \DateTimeImmutable();
        //  if ($started) $qb->andWhere($this->createQueryBuilder('a')->expr()->lte('a.startAt', 'CURRENT_TIMESTAMP()'));
        if ($started) {
            $qb->andWhere('a.startAt <= :now')->setParameter('now', $now);
        }

        return $qb
            ->getQuery()
            ->getOneOrNullResult();
    }

    public function getUserAnnouncements(User $user)
    {
        $qb = $this->createQueryBuilder('a')
            ->innerJoin('a.called', 'ac')
            // ->andWhere('ac.deletedAt IS NOT NULL')
            ->setParameter('user', $user);

        return $qb
            ->getQuery();
    }

    public function countOriginalAnnouncements($conditions)
    {
        $query = $this->createQueryBuilder('a')
            ->select('count(a.id)')
            ->andWhere('a.deletedAt IS NULL');

        if (!empty($conditions['category'])) {
            $query->leftJoin('a.course', 'c');
            $this->setProfessionalCategoriesQueryFilters($query, $conditions);
        }

        if (!empty($conditions['dateFrom'])) {
            $query->andWhere('a.createdAt >= :dateFrom')->setParameter('dateFrom', $conditions['dateFrom']);
        }
        if (!empty($conditions['dateTo'])) {
            $query->andWhere('a.createdAt <= :dateTo')->setParameter('dateTo', $conditions['dateTo']);
        }

        return $query->getQuery()->getSingleScalarResult();
    }

    /**
     * @return int Returns all total answers by challenge_questionid
     */
    public function findAnnouncementCourseAndUser($course, $user)
    {
        $sql = 'SELECT a.id, a.course_id, au.user_id, a.start_at, a.finish_at
            FROM announcement a
            INNER JOIN announcement_user au on au.announcement_id = a.id
            WHERE a.course_id = :course
            AND a.finish_at >= NOW() 
            AND au.user_id = :user
            AND a.deleted_at is null
        ';

        $stmt = $this->getEntityManager()->getConnection()->prepare($sql);
        $result = $stmt->executeQuery(['course' => $course, 'user' => $user])->fetchAllAssociative();

        return $result;
    }

    public function getCoursesUniques()
    {
        $query = $this->createQueryBuilder('a')
            ->select('c.id, c.name')
            ->distinct('c.id')
            ->leftJoin('a.course', 'c')
            ->getQuery()->getResult();

        return $query;
    }

    public function getAnnouncementCoursepres($idAnnouncement, $fieldsClients = ['default'])
    {
        $query = $this->createQueryBuilder('a')
            ->select($this->executeFunctionsAnnouncement($fieldsClients))
            ->where('a.id = :announcement')
            ->setParameter('announcement', $idAnnouncement)
            ->setMaxResults(1)
            ->getQuery()->getResult();

        return $query;
    }

    private function executeFunctionsAnnouncement($fieldsClients)
    {
        $functions = [];
        foreach ($fieldsClients as $fieldClient) {
            $functions[] = $this->{"{$fieldClient}FieldAnnouncement"}();
        }

        return $functions;
    }

    public function getAnnouncementbyCourse($course, $fieldsClients = ['default'])
    {
        $query = $this->createQueryBuilder('a')
            ->select($this->executeFunctionsAnnouncement($fieldsClients))
            ->where('a.course = :course')
            ->setParameter('course', $course)
            ->getQuery()->getResult();

        return $query;
    }

    private function handleApprovedCriteria(Announcement $announcement, $paramApproved)
    {
        $approvedCriteriaValues = [];
        //        $subsidized = $this->_em->getRepository(AnnouncementConfiguration::class)->createQueryBuilder('ac')
        //            ->select('ac')
        //            ->join('ac.configuration', 'c')
        //            ->where('c.id = :subsidizedId')
        //            ->setParameter('subsidizedId', AnnouncementConfigurationType::ID_ENABLE_SUBSIDIZED_COURSE)
        //            ->setMaxResults(1)
        //            ->getQuery()
        //            ->getOneOrNullResult()
        //        ;

        foreach ($this->_em->getRepository(AnnouncementCriteria::class)->findBy(['active' => true]) as $criteria) {
            $extra = $criteria->getExtra();
            $min = $extra['min'] ?? 0;
            $max = $extra['max'] ?? 100;
            $alwaysEnabled = $extra['alwaysEnabled'] ?? false;
            $selected = $paramApproved[$criteria->getId()] ?? [];
            $enabled = !empty($selected) && filter_var($selected['enabled'], FILTER_VALIDATE_BOOLEAN);
            $value = !empty($selected) ? $selected['value'] : 0;
            if ($enabled || $alwaysEnabled) {
                $value = max($value, $min);
                $value = min($value, $max);

                $approved = new AnnouncementAprovedCriteria();
                $approved->setAnnouncement($announcement)
                    ->setValue(\strval($value))
                    ->setAnnouncementCriteria($criteria);

                $approvedCriteriaValues[] = $approved;
            }
        }

        $announcement->setAnnouncementAprovedCriteria($approvedCriteriaValues);
    }

    public function saveBasicAnnouncementInfo(Announcement $announcement, Request $request)
    {
        $courseId = $request->get('courseId');
        if (empty($courseId)) {
            return [
                'Course ID is required',
            ];
        }
        $course = $this->_em->getRepository(Course::class)->find($courseId);
        if (!$course) {
            return ['Course not found'];
        }
        $code = $request->get('code');
        $startAt = $request->get('startAt');
        $finishAt = $request->get('finishAt');
        $usersPerGroup = $request->get('usersPerGroup', 0);
        $objectiveAndContents = $request->get('objectiveAndContent');
        $timezone = $request->get('timezone');
        $extra = $request->get('extra');
        if (empty($timezone)) {
            return [
                'ANNOUNCEMENT.FORM.TIMEZONE.REQUIRED',
            ];
        }
        $dateTimeZone = new \DateTimeZone($timezone);

        if (empty($startAt) || empty($finishAt)) {
            return [
                'ANNOUNCEMENT.FORM.ENTITY.REQUIRED_START_AT_FINISH_AT',
            ];
        }
        $totalHours = (float) $request->get('totalHours');

        $startAtDt = new \DateTimeImmutable($startAt, $dateTimeZone);
        $finishAtDt = new \DateTimeImmutable($finishAt, $dateTimeZone);
        if ($startAtDt > $finishAtDt) {
            return ['ANNOUNCEMENT.FORM.ENTITY.INVALID_START_AT_FINISH_AT'];
        }

        if (!empty($code)) {
            $announcement->setCode($code);
        }

        $announcement->setStartAt($startAtDt)
            ->setFinishAt($finishAtDt)
            ->setTotalHours($totalHours)
            ->setUsersPerGroup((int) $usersPerGroup)
            ->setObjectiveAndContents($objectiveAndContents)
            ->setCourse($course)
            ->setTimezone($timezone);

        if (!empty($extra)) {
            $existingExtra = $announcement->getExtra();
            if (!\is_array($existingExtra)) {
                $existingExtra = [];
            }
            foreach ($extra as $key => $value) {
                $existingExtra[$key] = $value;
            }
            $announcement->setExtra($existingExtra);
        }

        /** @var UploadedFile|null $file */
        $didacticGuide = $request->files->get('didacticGuide');
        $savedDidacticGuide = $announcement->getDidaticGuide();
        $isSubsidized = $this->_em->getRepository(AnnouncementConfiguration::class)
            ->findOneBy([
                'announcement' => $announcement,
                'configuration' => AnnouncementConfigurationType::ID_ENABLE_SUBSIDIZED_COURSE,
            ]);

        // If the announcement is subsidized, the didactic become required
        if ($isSubsidized && !$didacticGuide && !$savedDidacticGuide) {
            return ['ANNOUNCEMENT.FORM.ENTITY.DIDACTIC_GUIDE_REQUIRED'];
        }

        if ($didacticGuide) {
            if (!$this->isPdfFile($didacticGuide)) {
                return ['ANNOUNCEMENT.FORM.ENTITY.DIDACTIC_PDF_GUIDE_REQUIRED'];
            }
            $guide = new AnnouncementDidaticGuide();
            $guide->setFilenameFile($didacticGuide);
            $announcement->setDidaticGuide($guide);
        } else {
            // No file provided, check if exits as normal parameter
            $didacticGuideTemp = $request->get('didacticGuide');
            if (empty($didacticGuideTemp)) {
                // Delete operation needs to be performed
                $guide = $announcement->getDidaticGuide();
                if ($guide) {
                    $announcement->setDidaticGuide(null);
                }
            }
        }

        //        $approvedCriteriaValues = [];
        //        $paramApproved = $request->get('approvedCriteriaValues', []);

        //        foreach ($paramApproved as $key => $value) {
        //            if (filter_var($value['enabled'], FILTER_VALIDATE_BOOLEAN)) {
        //                $selected = $this->_em->getRepository(AnnouncementCriteria::class)->find($key);
        //                $newApproved = new AnnouncementAprovedCriteria();
        //                $newApproved->setAnnouncement($announcement)
        //                    ->setValue($value['value'])
        //                    ->setAnnouncementCriteria($selected);
        //                $approvedCriteriaValues[] = $newApproved;
        //            }
        //        }

        //        $announcement->setAnnouncementAprovedCriteria($approvedCriteriaValues);
        $this->handleApprovedCriteria($announcement, $request->get('approvedCriteriaValues', []));

        $configAnnouncement = $request->get('configAnnouncement');

        $isTemporalizationEnabled = $configAnnouncement
            && \array_key_exists('configuration-' . AnnouncementConfigurationType::ID_ENABLE_TEMPORALIZATION, $configAnnouncement)
            && filter_var($configAnnouncement['configuration-' . AnnouncementConfigurationType::ID_ENABLE_TEMPORALIZATION], FILTER_VALIDATE_BOOLEAN);

        $isCourseEnabledAtEnd = $configAnnouncement
            && \array_key_exists('configuration-' . AnnouncementConfigurationType::ID_ALLOW_ACTIVE_COURSE_AT_END, $configAnnouncement)
            && filter_var($configAnnouncement['configuration-' . AnnouncementConfigurationType::ID_ALLOW_ACTIVE_COURSE_AT_END], FILTER_VALIDATE_BOOLEAN);

        $isDigitalSignature = $configAnnouncement
            && \array_key_exists('configuration-' . AnnouncementConfigurationType::ID_DIGITAL_SIGNATURE, $configAnnouncement)
            && filter_var($configAnnouncement['configuration-' . AnnouncementConfigurationType::ID_DIGITAL_SIGNATURE], FILTER_VALIDATE_BOOLEAN);

        $isCost = $configAnnouncement
            && \array_key_exists('configuration-' . AnnouncementConfigurationType::ID_COST, $configAnnouncement)
            && filter_var($configAnnouncement['configuration-' . AnnouncementConfigurationType::ID_COST], FILTER_VALIDATE_BOOLEAN);

        $announcementConfigurationRepository = $this->_em->getRepository(AnnouncementConfiguration::class);

        $temporalizationConfiguration = $announcementConfigurationRepository
            ->enableTemporalization($announcement, $isTemporalizationEnabled);
        $announcementConfigurationRepository->enableAllowCourseAtEnd($announcement, $isCourseEnabledAtEnd);
        $announcementConfigurationRepository->enableDigitalSignature($announcement, $isDigitalSignature);
        $announcementConfigurationRepository->enableCost($announcement, $isCost);
        if ($temporalizationConfiguration) {
            $chapterTimingData = $request->get('chapterTiming', []);
            $temporalization = [];
            foreach ($chapterTimingData as $chapterId => $values) {
                $dtStart = empty($values['start']) ? $announcement->getStartAt() : new \DateTimeImmutable($values['start'], $dateTimeZone);
                $dtEnd = empty($values['end']) ? $announcement->getFinishAt() : new \DateTimeImmutable($values['end'], $dateTimeZone);
                $chapter = $this->_em->getRepository(Chapter::class)->find($chapterId);
                $timeInSeconds = 0;
                $chapterType = $chapter->getType();
                if ('game' !== $chapterType->getType()) {
                    $time = $values['time'] ?? '00:00:00';
                    $timeSeparated = explode(':', $time);
                    $hours = 0;
                    $minutes = 0;
                    $seconds = 0;
                    switch (\count($timeSeparated)) {
                        case 1:
                            $seconds = $timeSeparated[0]; // Only seconds
                            break;
                        case 2: // minutes:seconds
                            $minutes = $timeSeparated[0];
                            $seconds = $timeSeparated[1];
                            break;
                        case 3: // hours:minutes:seconds
                            $hours = $timeSeparated[0];
                            $minutes = $timeSeparated[1];
                            $seconds = $timeSeparated[2];
                            break;
                    }
                    $timeInSeconds = $seconds;
                    $timeInSeconds += $minutes * 60;
                    $timeInSeconds += $hours * 60 * 60;
                }

                $announcementTemporalization = new AnnouncementTemporalization();
                $announcementTemporalization->setAnnouncement($announcement)
                    ->setChapter($chapter)
                    ->setMinimumTime($timeInSeconds);

                $announcementTemporalization->setStartedAt($dtStart);
                $announcementTemporalization->setFinishedAt($dtEnd);
                $temporalization[] = $announcementTemporalization;
            }

            $announcement->setTemporalizations($temporalization);
        }

        $this->_em->persist($announcement);
        $this->_em->flush();

        $this->setMaterialCourseForAnnouncement($course, $announcement);

        return true;
    }

    public function getChapterTiming(Announcement $announcement): array
    {
        $chapterTiming = $this->createQueryBuilder('a')
            ->select('t.startedAt as start', 't.finishedAt as end', 't.minimumTime as minimumTime')
            ->addSelect('c.id', 'ct.name as typeName', 'c.description', 'c.title')
            ->addSelect('cour.id as courseId')
            ->addSelect('a.timezone')
            ->join('a.temporalizations', 't')
            ->leftJoin('t.chapter', 'c')
            ->leftJoin('c.course', 'cour')
            ->join('c.type', 'ct')
            ->where('a.id = :announcementId')
            ->setParameter('announcementId', $announcement->getId())
            ->getQuery()
            ->getResult();
        $chapterTiming = TimeZoneConverter::checkTimezone($chapterTiming);

        return $chapterTiming;
    }

    public function saveAnnouncementBonification(Request $request, Announcement $announcement)
    {
        $configAnnouncement = $request->get('configAnnouncement', []);
        $isEnabled = $configAnnouncement
            && \array_key_exists('configuration-' . AnnouncementConfigurationType::ID_ENABLE_SUBSIDIZED_COURSE, $configAnnouncement)
            && filter_var($configAnnouncement['configuration-' . AnnouncementConfigurationType::ID_ENABLE_SUBSIDIZED_COURSE], FILTER_VALIDATE_BOOLEAN);

        $this->_em->getRepository(AnnouncementConfiguration::class)->enableSubsidizedCourse($announcement, $isEnabled);

        if ($isEnabled) {
            $errors = [];
            $actionType = $request->get('actionType');
            if (empty($actionType)) {
                $errors[] = 'Action type is required';
            }

            $actionCode = $request->get('actionCode');
            if (empty($actionCode)) {
                $errors[] = 'Action Code is required';
            }

            $contactPerson = $request->get('contactPerson');
            if (empty($contactPerson)) {
                $errors[] = 'Contact Person is required';
            }

            $contactPersonEmail = $request->get('contactPersonEmail');
            if (empty($contactPersonEmail)) {
                $errors[] = 'Contact person email is required';
            }

            $contactPersonTelephone = $request->get('contactPersonTelephone');
            if (empty($contactPersonTelephone)) {
                $errors[] = 'Contact Person Telephone is required';
            }

            $didacticGuide = $announcement->getDidaticGuide();
            if (!$didacticGuide) {
                $errors[] = 'ANNOUNCEMENT.FORM.ENTITY.DIDACTIC_GUIDE_REQUIRED';
            }

            $usersPerGroup = $request->get('usersPerGroup', 0);

            if (\count($errors) > 0) {
                return $errors;
            }
            $announcement->setActionType($actionType)
                ->setActionCode($actionCode)
                ->setUsersPerGroup((int) $usersPerGroup)
                ->setContactPerson($contactPerson)
                ->setContactPersonEmail($contactPersonEmail)
                ->setContactPersonTelephone($contactPersonTelephone);

            /* Update approved criteria when is changed in bonus configuration */
            $this->handleApprovedCriteria($announcement, $request->get('approvedCriteriaValues', []));
        }
        $this->_em->persist($announcement);
        $this->_em->flush();

        return true;
    }

    public function saveStudents(Request $request, Announcement $announcement)
    {
        try {
            $userRepository = $this->_em->getRepository(User::class);
            $announcementGroupRepository = $this->_em->getRepository(AnnouncementGroup::class);
            $announcementUserRepository = $this->_em->getRepository(AnnouncementUser::class);

            $studentsGroups = $request->get('students', []);
            $idsRelations = [];
            $groups = [];
            foreach ($studentsGroups as $g) {
                if (false === strpos($g['id'], 'local')) {
                    $group = $announcementGroupRepository->find($g['id']);
                } else {
                    $group = new AnnouncementGroup();
                    $group->setAnnouncement($announcement);
                }

                $users = [];

                if (!empty($g['data'])) {
                    foreach ($g['data'] as $student) {
                        $user = $userRepository->find($student['id']);
                        $announcementUser = $announcementUserRepository->findOneBy(['user' => $user, 'announcement' => $announcement]);
                        if (!$announcementUser) {
                            $announcementUser = new AnnouncementUser();
                            $announcementUser
                                ->setAnnouncement($announcement)
                                ->setUser($user);
                        }
                        $announcementUser->setAnnouncementGroup($group);

                        $users[] = $announcementUser;
                    }
                }
                $group->setAnnouncementUsers($users);
                $groups[] = $group;

                $idsRelations[$g['id']] = $group->getId();
            }

            $announcement->setAnnouncementGroups($groups);
            $this->_em->persist($announcement);
            $this->_em->flush();

            // Reorder the groups and set the group number
            $this->_em->getRepository(AnnouncementGroup::class)->refreshAnnouncementExternGroups($announcement);
            $this->_em->getRepository(AnnouncementGroup::class)->refreshGroupNumbers($announcement);

            return [
                'error' => false,
                'data' => $this->getAnnouncementStudentsWithGroup($announcement),
            ];
        } catch (\Exception $e) {
            return [
                'error' => true,
                'data' => $e->getMessage(),
            ];
        }
    }

    public function getAnnouncementStudentsWithGroup(Announcement $announcement)
    {
        $groups = $this->_em->getRepository(AnnouncementGroup::class)->findBy(['announcement' => $announcement]);
        $data = [];

        foreach ($groups as $g) {
            $users = $this->_em->getRepository(AnnouncementUser::class)->createQueryBuilder('au')
                ->select('u.id', 'u.email', 'u.firstName', 'u.lastName')
                ->join('au.user', 'u')
                ->where('au.announcementGroup =:group')
                ->setParameter('group', $g)
                ->getQuery()
                ->getResult();

            $tutor = $this->_em->getRepository(AnnouncementTutor::class)->getTutorByGroup($g, true);

            $sessions = [];
            /** @var AnnouncementGroupSessionRepository $groupSessionsRepository */
            $groupSessions = $this->_em->getRepository(AnnouncementGroupSession::class)->getSessions($g);

            foreach ($groupSessions as $session) {
                $classroom = $session->getClassroomvirtual();

                $sessions[] = [
                    'id' => $session->getId(),
                    'startAt' => $session->getStartAt(),
                    'finishAt' => $session->getFinishAt(),
                    'url' => $session->getUrl(),
                    'session_number' => $session->getSessionNumber(),
                    'classroomId' => $classroom ? $classroom->getId() : null,
                    'place' => $session->getPlace(),
                    'entryMargin' => $session->getEntryMargin(),
                    'exitMargin' => $session->getExitMargin(),
                    'timezone' => $session->getTimezone(),
                    'cost' => $session->getCost() ?? 0,
                    'type' => $session->getType() ?? TypeSession::TYPE_PRESENTIAL,
                    'modality' => $session->getModality() ? [
                        'id' => $session->getModality()->getId(),
                        'name' => $session->getModality()->getName(),
                    ] : null,
                    'virtual_meeting_id' => $session->getVirtualMeetingId(),
                ];
            }

            $data[] = [
                'id' => $g->getId(),
                'companyProfile' => $g->getCompanyProfile(),
                'code' => $g->getCode(),
                'companyCif' => $g->getCompanyCif(),
                'denomination' => $g->getDenomination(),
                'fileNumber' => $g->getFileNumber(),
                'tutor' => $tutor,
                'data' => $users,
                'numberOfSessions' => $g->getNumSessions(),
                'place' => $g->getPlace(),
                'cost' => $g->getCost(),
                'typeMoney' => $this->getTypeMoneyGroup($g),
                'sessions' => $sessions,
            ];
        }

        return $data;
    }

    private function getTypeMoneyGroup(AnnouncementGroup $announcementGroup)
    {
        $typeMoney = $announcementGroup->getTypeMoney();
        if ($typeMoney) {
            return [
                'id' => $typeMoney->getId(),
                'name' => $typeMoney->getName(),
                'symbol' => $typeMoney->getSymbol(),
                'codeIso' => $typeMoney->getCodeIso(),
                'country' => $typeMoney->getCountry(),
                'fractionalUnit' => $typeMoney->getFractionalUnit(),
                'fullName' => $typeMoney->getFullName(),
            ];
        }

        return null;
    }

    public function saveGroupInformation(Request $request, Announcement $announcement)
    {
        try {
            $groupsInfo = json_decode($request->get('groups'), true);
            $announcementGroupRepository = $this->_em->getRepository(AnnouncementGroup::class);

            foreach ($groupsInfo as $info) {
                /** @var AnnouncementGroup $group */
                $group = $announcementGroupRepository->find($info['id']);
                // Common information
                $companyProfile = $info['companyProfile'];
                $code = $info['code'];
                $companyCif = $info['companyCif'];
                $denomination = $info['denomination'];
                $fileNumber = $info['fileNumber'];

                if ($announcement->getSubsidized()) {
                    // Information required when the announcement is subsidized
                    $errors = [];

                    if (empty($companyProfile)) {
                        $errors[] = 'Profile is required';
                    }
                    if (empty($code)) {
                        $errors[] = 'Code is required';
                    }
                    if (empty($companyCif)) {
                        $errors[] = 'CIF is required';
                    }
                    if (empty($denomination)) {
                        $errors[] = 'Denomination is required';
                    }
                    if (empty($fileNumber)) {
                        $errors[] = 'File number is required';
                    }

                    if (\count($errors) > 0) {
                        return $errors;
                    }
                }

                $group->setCompanyProfile($companyProfile)
                    ->setCode($code)
                    ->setCompanyCif($companyCif)
                    ->setDenomination($denomination)
                    ->setFileNumber($fileNumber);
            }

            $this->_em->flush();

            return true;
        } catch (\Exception $e) {
            return [$e->getMessage()];
        }
    }

    public function generateAnnouncementUrl(
        UserPasswordHasherInterface $userPasswordHasher,
        User $user,
        Announcement $announcement,
        string $baseUrl = ''
    ): array {
        $access = $announcement->getAnnouncementInspectorAccess();
        if (!$access) {
            $userToken = new UserToken();

            $username = bin2hex(random_bytes(6));
            $password = bin2hex(random_bytes(8));

            /** @var AnnouncementInspectorAccess $currentInspector */
            $currentInspector = $this->_em->getRepository(AnnouncementInspectorAccess::class)->find($username);
            if ($currentInspector) {
                while (true) {
                    $username = bin2hex(random_bytes(6));
                    /** @var AnnouncementInspectorAccess $currentInspector */
                    $currentInspector = $this->_em->getRepository(AnnouncementInspectorAccess::class)->find($username);
                    if (!$currentInspector) {
                        break;
                    } // If not found, username is unique and correct
                }
            }

            $data = [
                'id' => $announcement->getId(),
                'code' => $announcement->getCode(),
                'user' => $username,
                'password' => $password,
            ];

            $token = hash('sha256', json_encode($data));
            $userToken->setUser($user)
                ->setToken($token)
                ->setType(UserToken::TYPE_ANNOUNCEMENT_INSPECTOR)
                ->setValidUntil((new \DateTimeImmutable())->modify('+5 years'))
                ->setExtra($data);

            $access = new AnnouncementInspectorAccess();
            $access->setAnnouncement($announcement)
                ->setToken($userToken)
                ->setUser($data['user']);
            $access->setPassword($userPasswordHasher->hashPassword($access, $data['password']));

            $this->_em->persist($access);
            $this->_em->flush();
        } else {
            $userToken = $access->getToken();
            $extra = $userToken->getExtra();
            $token = $userToken->getToken();
            $data = [
                'user' => $extra['user'],
                'password' => $extra['password'],
            ];
        }

        return [
            'user' => $data['user'],
            'password' => $data['password'],
            'url' => "$baseUrl/inspector/view?token=$token",
        ];
    }

    public function getDirectChatChannel(Announcement $announcement): ?ChatChannel
    {
        $server = $this->_em->getRepository(ChatServer::class)->getServer(ChatServer::TYPE_ANNOUNCEMENT, $announcement->getId(), true);
        $channel = $this->_em->getRepository(ChatChannel::class)->findOneBy([
            'server' => $server,
            'type' => Announcement::CHAT_CHANNEL_DIRECT,
            'parent' => null,
        ]);

        if (!$channel) {
            $channel = $this->_em->getRepository(ChatChannel::class)->createDirectChannel($server, null, 'CHAT');
        }

        return $channel;
    }

    public function getForumChannel(Announcement $announcement): ?ChatChannel
    {
        $server = $this->_em->getRepository(ChatServer::class)->getServer(ChatServer::TYPE_ANNOUNCEMENT, $announcement->getId(), true);
        $channel = $this->_em->getRepository(ChatChannel::class)->findOneBy([
            'server' => $server,
            'type' => Announcement::CHAT_CHANNEL_FORUM,
            'parent' => null,
        ]);
        if (!$channel) {
            $channel = $this->_em->getRepository(ChatChannel::class)
                ->createNormalChannel($server, null, 'FORUM', Announcement::CHAT_CHANNEL_FORUM);
        }

        return $channel;
    }

    private function defaultFieldAnnouncement()
    {
        return 'a.id, a.startAt, a.finishAt, a.subsidized, 	a.maxUsers, a.formativeActionType, 
            a.format, a.totalHours, a.place, a.trainingCenter, a.trainingCenterAddress, a.trainingCenterNif, 
            a.trainingCenterTeacher, a.trainingCenterTeacherDni, a.trainingCenterPhone, a.trainingCenterEmail, 
            a.subsidizerEntity, a.generalInformation, a.totalHours, a.trainingCenterTeacher, a.trainingCenterTeacherDni,
            a.provider, a.providerCif';
    }

    private function imqFieldAnnouncement()
    {
        return 'a.afType, a.formationLevel, a.numberOfParticipantsAf, a.numberOfGroups, a.directedTo, 
                a.selectionCriteria, a.contactPerson';
    }

    private function iberostarFieldAnnouncement()
    {
        return 'a.hotel, a.society, a.subsidizedGroup, a.department, a.clientContactPerson, 
                 a.selectionCriteria, a.contactPerson';
    }

    public function validateInspectorHash(User $user, string $sHash)
    {
        /** @var UserTokenRepository $userTokenRepository */
        $userTokenRepository = $this->_em->getRepository(UserToken::class);
        $userToken = $userTokenRepository->findOneBy([
            'token' => $sHash,
            'type' => UserToken::TYPE_ANNOUNCEMENT_INSPECTOR,
        ]);

        if (!$userToken) {
            return new JsonResponse([
                'error' => true,
                'data' => 'Invalid token',
            ], Response::HTTP_UNAUTHORIZED);
        }

        $base64 = base64_encode(json_encode($userToken->getExtra()));
        $hashed = hash('sha256', $base64);
        if ($sHash !== $hashed) {
            return new JsonResponse([
                'error' => true,
                'data' => 'Hash has been corrupted',
            ], Response::HTTP_UNAUTHORIZED);
        }

        if (!\in_array(User::ROLE_ADMIN, $user->getRoles())) {
            if (
                $userToken->getUser()->getId() !== $user->getId()
                || !\in_array(User::ROLE_INSPECTOR, $user->getRoles())
            ) {
                return new JsonResponse(
                    [
                        'error' => true,
                        'data' => 'Unauthorized',
                    ],
                    Response::HTTP_UNAUTHORIZED
                );
            }
        }

        return true;
    }

    public function isAnnouncementBeforeFundae($idAnnouncement, $date)
    {
        return $this->createQueryBuilder('a')
            ->select('a.id')
            ->andWhere('a.id = :idAnnouncement')
            ->andWhere('a.createdAt < :date')
            ->setParameter('idAnnouncement', $idAnnouncement)
            ->setParameter('date', $date)
            ->getQuery()->getResult();
    }

    public function findAnnouncementsByTypeCourse(User $user, $typesCourses = [])
    {
        // Array con los IDs de los tipos de curso: 2 = On site, 4 = Virtual classroom
        // 1 TELEFORMACION 3 MIXTO
        $qb = $this->_em->createQueryBuilder();

        $qb->select('au', 'a', 'u', 'c')
            ->from(AnnouncementUser::class, 'au');
        $qb->join('au.announcement', 'a')
            ->join('a.course', 'c')
            ->join('c.typeCourse', 'tc')
            ->join('au.user', 'u')
            ->where('u = :user')
            ->andWhere('tc.id IN (:typesCourses)')
            ->andWhere('au.isAproved = true')
            ->setParameter('user', $user)
            ->setParameter('typesCourses', $typesCourses)
            ->orderBy('a.id', 'ASC');

        return $qb->getQuery()->getResult();
    }

    public function findAnnouncementFinishAndRangeDate($dateInitial, $dateFinish)
    {
        return $this->createQueryBuilder('a')
            ->select('a.id')
            ->andWhere('a.finishAt >= :dateInial')
            ->andWhere('a.finishAt <= :dateFinisn')
            ->andWhere('a.deletedAt is null')
            ->setParameter('dateInial', $dateInitial)
            ->setParameter('dateFinisn', $dateFinish)
            ->getQuery()->getResult();
    }

    /**
     * Find announcements by user and optional date filtering.
     *
     * @param User $user      the user for whom to find announcements
     * @param bool $dateStart whether to filter by the start date
     *
     * @return Announcement[] an array of Announcement objects
     */
    public function findAnnouncementByUser(User $user, $dateStart = true)
    {
        $queryBuilder = $this->createQueryBuilder('a')
            ->innerJoin('a.called', 'called')
            ->innerJoin('a.course', 'course')
            ->andWhere('called.user = :user')
            ->andWhere('course.active = true')
            ->andWhere('a.notifiedAt is not null');

        $queryBuilder->andWhere($queryBuilder->expr()->orX(
            'a.status = :statusActive',
            'a.status = :statusFinished'
        ));

        $queryBuilder
            ->orderBy('a.finishAt', 'DESC')
            ->setParameter('user', $user)
            ->setParameter('statusActive', Announcement::STATUS_ACTIVE)
            ->setParameter('statusFinished', Announcement::STATUS_FINISHED);

        $query = $queryBuilder->getQuery();
        $results = $query->getResult();

        $announcements = [];

        foreach ($results as $result) {
            $announcement = $this->find($result->getId());
            $timeZone = $announcement->getTimezone() ?? 'Europe/Madrid';

            $nowInTimeZone = new \DateTimeImmutable('now', new \DateTimeZone($timeZone));

            if ($announcement->getStartAt() <= $nowInTimeZone) {
                $announcements[] = $announcement;
            }
        }

        return $announcements;
    }

    public function findAnnouncementUserNotified($course, $user)
    {
        $query = $this->createQueryBuilder('a')
            ->select('a')
            ->join('a.called', 'au')
            ->where('a.course =:course')
            ->andWhere('au.user =:user')
            ->andWhere('a.notifiedAt is not null');

        $query->andWhere($query->expr()->orX(
            'a.status = :statusActive',
            'a.status = :statusFinished'
        ));

        $query->setParameters([
            'course' => $course,
            'user' => $user,
            'statusActive' => Announcement::STATUS_ACTIVE,
            'statusFinished' => Announcement::STATUS_FINISHED,
        ]);

        return $query->orderBy('a.finishAt', 'DESC')
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    public function findUsersByAnnouncement($announcement)
    {
        return $this->createQueryBuilder('a')
            ->select('au.id')
            ->join('a.called', 'au')
            ->join('au.user', 'u')
            ->andWhere('a.id = :announcement')
            ->andWhere('u.deletedAt IS NULL')
            ->setParameter('announcement', $announcement)
            ->getQuery()
            ->getResult();
    }

    public function findAnnouncementUserByGroup($announcementGroup, $announcement)
    {
        return $this->createQueryBuilder('a')
            ->select('au.id')
            ->join('a.called', 'au')
            ->join('au.user', 'u')
            ->andWhere('a.id = :announcement')
            ->andWhere('au.announcementGroup = :announcementGroup')
            ->andWhere('u.deletedAt IS NULL')
            ->setParameter('announcement', $announcement)
            ->setParameter('announcementGroup', $announcementGroup)
            ->getQuery()
            ->getResult();
    }

    public function hasBonification(Announcement $announcement): bool
    {
        return $this->announcementConfigurationsService->hasBonification($announcement);
    }

    public function getAnnoucementForInAnnoucementsIds($announcementIds)
    {
        $announcementQuery = $this->createQueryBuilder('a');

        return $announcementQuery->select('a')
            ->orderBy('a.id', 'ASC')
            ->where($announcementQuery->expr()->in('a.id', $announcementIds))
            ->getQuery()
            ->getResult();
    }

    public function getAnnouncementInfo()
    {
        return $this->createQueryBuilder('a')
            ->select('a.id', 'a.startAt', 'a.finishAt')
            ->addSelect('c.id as courseId', 'c.code', 'c.name')
            ->leftJoin('a.course', 'c')
            ->groupBy('a.id')
            ->getQuery()->getResult();
    }

    public function findUsersInAnnouncementByCourse(Course $course): array
    {
        $result = $this->createQueryBuilder('a')
            ->select('a.id as announcementId', 'u.id as userId')
            ->innerJoin('a.called', 'c')
            ->innerJoin('c.user', 'u')
            ->andWhere('a.deletedAt IS NULL')
            ->andWhere('a.notifiedAt IS NOT NULL')
            ->andWhere('a.course = :course')
            ->setParameter('course', $course)
            ->addGroupBy('u.id')
            ->getQuery()
            ->getResult();

        return empty($result) ? [] : array_column($result, 'userId');
    }

    /**
     * @throws Exception
     */
    public function getAnnouncementParticipants(?\DateTime $startDate = null, ?\DateTime $endDate = null, $status = null, $lang = 'en', $extra = null, ?User $user = null): array
    {
        $translations = $this->getStatusTranslations($lang);

        $qb = $this->getEntityManager()->getConnection()->createQueryBuilder()
            ->select(
                "CONCAT(COALESCE(MAX(a.code),''), '_', COALESCE(a.id,''), COALESCE(CONCAT('-',ag.id),''), COALESCE(CONCAT('-',ags.id),'')) AS CODFORM",
                'MAX(cc.name) AS Categoria',
                'MAX(c.name) AS NombreFormacion',
                'MAX(c.locale) AS Idioma',
                'MAX(a.timezone) AS Tz',
                'MAX(ags.place) AS LugarImparticion',
                'CASE 
                    WHEN MAX(ags.id) IS NOT NULL THEN MAX(ags.start_at)
                    ELSE MAX(a.start_at)
                END AS FechaInicio',
                'CASE 
                    WHEN MAX(ags.id) IS NOT NULL THEN MAX(ags.finish_at)
                    ELSE MAX(a.finish_at)
                END AS FechaFin',
                'CASE
                    WHEN MAX(ags.id) IS NOT NULL THEN ROUND(TIMESTAMPDIFF(MINUTE, MAX(ags.start_at), MAX(ags.finish_at)) / 60.0, 2)
                    ELSE MAX(a.total_hours)
                END AS Horas',
                'MAX(tc.name) AS ModalidadCurso',
                "CASE 
                    WHEN MAX(am.name) IS NOT NULL THEN MAX(am.name)
                    WHEN MAX(tc.code) = 'online' THEN MAX(tc.name)
                    ELSE ''
                END AS ModalidadSesion",
                "CASE WHEN MAX(tc.denomination) = 'extern' THEN 'EXT' ELSE 'INT' END AS ExtInt",
                "CASE WHEN MAX(au.external) = 1 THEN 'Libre' ELSE 'Asignado' END AS Obligatoriedad",
                "CONCAT(COALESCE(MAX(u.first_name), ''), ' ', COALESCE(MAX(u.last_name), '')) AS ProveedorTutor",
                'MAX(u.email) AS CorreoTutor',
                "CONCAT(COALESCE(MAX(ag.cost), '0.00'), ' ', COALESCE(MAX(tmg.symbol), COALESCE(MAX(tmg.code_iso), ''))) AS CosteMonedaLocal",
                "'' AS CosteEuros",
                'MAX(uct.name) AS Sociedad',
                'MAX(a.status) AS Estado',
            )
            ->from('announcement', 'a')
            ->innerJoin('a', 'course', 'c', 'c.id = a.course_id')
            ->leftJoin('c', 'type_course', 'tc', 'tc.id = c.type_course_id')
            ->leftJoin('c', 'course_category', 'cc', 'cc.id = c.category_id')
            ->leftJoin('a', 'announcement_group', 'ag', 'ag.announcement_id = a.id')
            ->leftJoin('ag', 'announcement_group_session', 'ags', 'ags.announcement_group_id = ag.id')
            ->leftJoin('ags', 'announcement_modality', 'am', 'am.id = ags.modality_id')
            ->leftJoin('ag', 'announcement_tutor', 'at', 'at.announcement_group_id = ag.id')
            ->leftJoin('at', 'user', 'u', 'u.id = at.tutor_id')
            ->leftJoin('u', 'user_company', 'uct', 'uct.id = at.user_company_id')
            ->leftJoin('u', 'announcement_user', 'au', 'au.user_id = u.id')
            ->leftJoin('ags', 'type_money', 'tm', 'tm.id = ags.type_money_id')
            ->leftJoin('ag', 'type_money', 'tmg', 'tmg.id = ag.type_money_id')
            ->where('a.deleted_at IS NULL')
            ->groupBy('a.id', 'ag.id', 'ags.id')
            ->orderBy('ags.id', 'ASC');

        if ($startDate) {
            $qb->andWhere('a.finish_at >= :startDate')
                ->setParameter('startDate', $startDate->format('Y-m-d H:i:s'));
        }

        if ($endDate) {
            $qb->andWhere('a.finish_at <= :endDate')
                ->setParameter('endDate', $endDate->format('Y-m-d H:i:s'));
        }

        if (!empty($status) && Announcement::STATUS_ALL !== $status && \in_array($status, $this->allowedStatuses, true)) {
            $qb->andWhere('a.status = :status')
                ->setParameter('status', $status);
        }

        $qb = $this->announcementExtraService->addExtraSelectsQueryBuilder($qb, $extra);
        $qb = $this->announcementExtraService->applyExtraFiltersQueryBuilder($qb, $extra);

        if ($user && $user->isManager() && !$user->isAdmin()) {
            $qb->leftJoin('c', 'course_manager', 'cm', 'cm.course_id = c.id')
                ->andWhere('cm.user_id = :userId OR a.created_by_id = :userId')
                ->setParameter('userId', $user->getId());
        }

        $results = $qb->execute()->fetchAllAssociative();

        foreach ($results as &$row) {
            $statusValue = $row['Estado'];
            $row['Estado'] = $translations[$statusValue] ?? "$statusValue (" . $translations['UNKNOWN'] . ')';
        }

        return $results;
    }

    /**
     * @throws Exception
     */
    public function getAnnouncementParticipantsDetail(?\DateTime $startDate = null, ?\DateTime $endDate = null, $status = null, $lang = 'en', $extra = null, ?User $user = null): array
    {
        $qb = $this->getEntityManager()->getConnection()->createQueryBuilder()
            ->select(
                "CONCAT(COALESCE(MAX(a.code),''), '_', COALESCE(MAX(a.id),''), COALESCE(CONCAT('-',MAX(ag.id)),''), COALESCE(CONCAT('-',MAX(ags.id)),'')) AS CodificacionCurso",
                "COALESCE(JSON_UNQUOTE(JSON_EXTRACT(MAX(u.meta), '$.HRP')), '') AS CodEmpleado",
                'MAX(u.first_name) AS Nombre',
                'MAX(u.last_name) AS Apellido1',
                "'' AS Apellido2",
                'MAX(u.email) AS CorreoElectronico',
                'MAX(ue.birthdate) AS FechaNacimiento',
                '"" AS PorcentajeAsistencia',
                'MAX(ue.gender) AS Genero',
                "MAX(CASE WHEN fc_c.name = 'Centro' THEN f_c.name ELSE NULL END) AS CentroTrabajo",
                'MAX(uct.name) AS Sociedad',
                "MAX(CASE WHEN fc_c.name = 'Departamento' THEN f_c.name ELSE NULL END) AS Departamento",
                'CASE
                    WHEN MAX(ags.id) IS NOT NULL THEN TIMESTAMPDIFF(MINUTE, ags.start_at, ags.finish_at) / 60.0
                    ELSE MAX(a.total_hours)
                END AS Horas',
                "CASE
                    WHEN MAX(u.roles) LIKE '%\"ROLE_MANAGER\"%' THEN 'Manager'
                    WHEN MAX(u.roles) LIKE '%\"ROLE_TUTOR\"%' THEN 'Tutor'
                    WHEN MAX(u.roles) LIKE '%\"ROLE_SUPER_ADMIN\"%' OR MAX(u.roles) LIKE '%\"ROLE_ADMIN\"%' THEN 'Admin'
                    ELSE 'Personal Base'
                END AS Colectivo",
                "COALESCE(JSON_UNQUOTE(JSON_EXTRACT(MAX(u.meta), '$.extraFields')), '') AS extraFields"
            )
            ->from('user', 'u')
            ->innerJoin('u', 'announcement_user', 'au', 'au.user_id = u.id')
            ->innerJoin('au', 'announcement', 'a', 'a.id = au.announcement_id')
            ->innerJoin('a', 'course', 'c', 'c.id = a.course_id')
            ->leftJoin('au', 'announcement_group', 'ag', 'ag.id = au.announcement_group_id')
            ->leftJoin('ag', 'announcement_group_session', 'ags', 'ags.announcement_group_id = ag.id')
            ->leftJoin('ag', 'announcement_tutor', 'at', 'at.announcement_group_id = ag.id')
            ->leftJoin('at', 'user_company', 'uct', 'uct.id = at.user_company_id')
            ->leftJoin('u', 'user_extra', 'ue', 'ue.user_id = u.id')
            ->leftJoin('u', 'user_filter', 'uf_c', 'uf_c.user_id = u.id')
            ->leftJoin('uf_c', 'filter', 'f_c', 'f_c.id = uf_c.filter_id')
            ->leftJoin('f_c', 'filter_category', 'fc_c', 'fc_c.id = f_c.filter_category_id')
            ->where('a.deleted_at IS NULL')
            ->groupBy('a.id', 'ag.id', 'ags.id', 'u.id')
            ->orderBy('ags.id', 'DESC')
            ->addOrderBy('u.id', 'DESC');

        if ($startDate) {
            $qb->andWhere('a.finish_at >= :startDate')
                ->setParameter('startDate', $startDate->format('Y-m-d H:i:s'));
        }

        if ($endDate) {
            $qb->andWhere('a.finish_at <= :endDate')
                ->setParameter('endDate', $endDate->format('Y-m-d H:i:s'));
        }

        if (!empty($status) && Announcement::STATUS_ALL !== $status && \in_array($status, $this->allowedStatuses, true)) {
            $qb->andWhere('a.status = :status')
                ->setParameter('status', $status);
        }

        $qb = $this->announcementExtraService->applyExtraFiltersQueryBuilder($qb, $extra);

        if ($user && $user->isManager() && !$user->isAdmin()) {
            $qb->leftJoin('c', 'course_manager', 'cm', 'cm.course_id = c.id')
                ->andWhere('cm.user_id = :userId OR a.created_by_id = :userId')
                ->setParameter('userId', $user->getId());
        }

        $results = $qb->execute()->fetchAllAssociative();

        // Process assistance data for each result
        $results = $this->processAssistanceDataForParticipantsDetail($results);

        return $results;
    }

    public function getStatusTranslations(string $lang): array
    {
        return [
            Announcement::STATUS_CONFIGURATION => $this->translator->trans('announcement.status.configuration', [], 'announcement', $lang),
            Announcement::STATUS_ACTIVE => $this->translator->trans('announcement.status.active', [], 'announcement', $lang),
            Announcement::STATUS_IN_PROGRESS => $this->translator->trans('announcement.status.in_progress', [], 'announcement', $lang),
            Announcement::STATUS_FINISHED => $this->translator->trans('announcement.status.finished', [], 'announcement', $lang),
            Announcement::STATUS_INACTIVE => $this->translator->trans('announcement.status.inactive', [], 'announcement', $lang),
            Announcement::STATUS_ARCHIVED => $this->translator->trans('announcement.status.archived', [], 'announcement', $lang),
            'UNKNOWN' => $this->translator->trans('announcement.status.others', [], 'announcement', $lang),
        ];
    }

    private function setMaterialCourseForAnnouncement(Course $course, Announcement $announcement)
    {
        $MaterialsCourse = $this->em->getRepository(MaterialCourse::class)->findBy(['course' => $course, 'announcement' => null]);
        if ($MaterialsCourse) {
            foreach ($MaterialsCourse as $materialCourse) {
                $cloneMaterialCourse = $this->em->getRepository(MaterialCourse::class)->findBy(['announcement' => $announcement, 'parentId' => $materialCourse->getId()]);
                if (!$cloneMaterialCourse) {
                    $cloneMaterialCourse = clone $materialCourse;
                    $cloneMaterialCourse->setAnnouncement($announcement);
                    $cloneMaterialCourse->setIsVisible(true);
                    $cloneMaterialCourse->setIsDownload(true);
                    $cloneMaterialCourse->setType(MaterialsCourseEnum::CLONE_MATERIALS_COURSE);
                    $cloneMaterialCourse->setParentId($materialCourse->getId());
                    $this->em->persist($cloneMaterialCourse);
                }
            }
            $this->em->flush();
        }
    }

    /**
     * @throws \DateMalformedStringException
     * @throws NonUniqueResultException
     * @throws NoResultException
     */
    public function getAnnouncementsPaginated(Request $request, int $page, User $user): array
    {
        $isAdmin = $user->isAdmin();
        $isManager = $user->isManager() || $user->isManagerEditor();
        $isTutor = $user->isTutor();

        if (!($isAdmin || $isManager || $isTutor)) {
            return ['data' => [], 'totalItems' => 0];
        }

        $filter = $request->get('query');
        $query = $this->createQueryBuilder('a')
            ->leftJoin('a.course', 'c')
            ->leftJoin('c.typeCourse', 't')
            ->andWhere('t.active = 1');

        $fromDate = $request->get('fromDate');
        $toDate = $request->get('toDate');
        $type = $request->get('type');
        $status = $request->get('status');

        $extra = $request->get('extra');
        if (\is_string($extra)) {
            $extra = json_decode($extra, true);
        }

        if (!$isAdmin) {
            if ($isManager || $isTutor) {
                $orX = $query->expr()->orX();

                if ($isManager) {
                    $orX->add($query->expr()->eq('a.createdBy', ':user'));

                    if ($this->settings->get('app.announcement.managers.sharing')) {
                        $query->leftJoin('a.announcementManagers', 'm');
                        $orX->add($query->expr()->eq('m.manager', ':user'));
                    }
                }

                if ($isTutor) {
                    $query->leftJoin('a.tutors', 'tut');
                    $orX->add($query->expr()->eq('tut.tutor', ':user'));
                }

                $query->andWhere($orX)->setParameter('user', $user);
            }
        }

        if (!empty($fromDate) && !empty($toDate)) {
            $query->andWhere('a.startAt >= :fromDate AND a.startAt <= :toDate')
                ->setParameter('fromDate', new \DateTime($fromDate))
                ->setParameter('toDate', new \DateTime($toDate));
        } else {
            if (!empty($fromDate)) {
                $query->andWhere('a.startAt >= :startAt')->setParameter('startAt', new \DateTime($fromDate));
            }

            if (!empty($toDate)) {
                $query->andWhere('a.startAt <= :finishAt')->setParameter('finishAt', new \DateTime($toDate));
            }
        }

        if (!empty($filter)) {
            $query->andWhere('c.name LIKE :filter OR c.code LIKE :filter')
                ->setParameter('filter', "%$filter%");
        }

        if (!empty($status) && 'ALL' !== $status) {
            $query->andWhere('a.status =:status')
                ->setParameter('status', $status);
        }

        if (!empty($type)) {
            $query->andWhere('t.id = :type_course_id');
            $query->setParameter('type_course_id', $type);
        }

        $this->announcementExtraService->applyExtraFiltersQueryBuilderJson($query, $extra);

        $countQuery = clone $query;
        $totalItems = $countQuery->select('COUNT(DISTINCT a.id) as total')
            ->getQuery()
            ->getSingleScalarResult();

        $pageSize = 10;

        $query->select(
            'a.id',
            'a.code as announcementCode',
            'a.startAt',
            'a.finishAt',
            'c.code AS courseCode',
            't.id as idTypeCourse',
            't.name as modality',
            't.denomination',
            'c.name as courseName',
            'COUNT(call.id) as total',
            'c.image'
        )
            ->addSelect('a.status', 'a.timezone')
            ->addSelect('t.denomination as source')
            ->leftJoin('a.called', 'call')
            ->groupBy('a.id')
            ->setMaxResults($pageSize)
            ->setFirstResult(($page - 1) * $pageSize);

        $orderBy = $request->get('order', null);
        if (!empty($orderBy)) {
            $direction = $request->get('direction', 'ASC');
            switch ($orderBy) {
                case 'code':
                    $query->addOrderBy('a.code', $direction);
                    break;
                case 'startAt':
                    $query->addOrderBy('a.startAt', $direction);
                    break;
                case 'finishAt':
                    $query->addOrderBy('a.finishAt', $direction);
                    break;
                case 'name':
                    $query->addOrderBy('c.name', $direction);
                    break;
                case 'total':
                    $query->addOrderBy('total', $direction);
                    break;
                default:
                    $query->addOrderBy('a.id', 'desc');
            }
        } else {
            $query->addOrderBy('a.id', 'desc');
        }

        $query->andWhere('c.deletedAt IS NULL');

        // dd($query->getQuery()->getSQL());
        return ['data' => $query->getQuery()->getResult(), 'totalItems' => $totalItems];
    }

    public function getAnnouncementsUsersByParameters(Course $course, int $announcementId, string $startAt, string $finishAt)
    {
        $results = $this->createQueryBuilder('a')
            ->select('a.id', 'u.id as userId')
            ->join('a.called', 'cal')
            ->join('cal.user', 'u')
            ->where('a.course =:course AND a.id != :announcement_id')
            ->andWhere('(a.startAt <= :start_at AND a.finishAt >= :start_at) OR (a.startAt <= :finish_at AND a.finishAt >= :finish_at) OR (a.startAt <= :start_at AND a.finishAt >= :finish_at) OR (a.startAt >= :start_at AND a.finishAt <= :finish_at)')
            ->andWhere('a.status = :statusConfiguration OR a.status = :statusActive')

            ->setParameters([
                'course' => $course,
                'announcement_id' => $announcementId,
                'start_at' => new \DateTimeImmutable($startAt),
                'finish_at' => new \DateTimeImmutable($finishAt),
                'statusConfiguration' => Announcement::STATUS_CONFIGURATION,
                'statusActive' => Announcement::STATUS_ACTIVE,
            ])
            ->getQuery()
            ->getResult();

        $usersIds = [];
        foreach ($results as $r) {
            $usersIds[] = $r['userId'];
        }

        return $usersIds;
    }

    private function processAssistanceDataForParticipantsDetail(array $results): array
    {
        foreach ($results as &$row) {
            $participantCode = $row['CodificacionCurso'] ?? '';

            if (!$this->isValidParticipantCode($participantCode)) {
                $row['PorcentajeAsistencia'] = '';
                continue;
            }

            $sessionId = $this->extractSessionIdFromParticipantCode($participantCode);
            $userId = $this->extractUserIdFromRow($row);

            if ($sessionId > 0 && $userId > 0) {
                $row['PorcentajeAsistencia'] = (string) $this->getAssistancePercentForUserInSession(
                    userId: $userId,
                    sessionId: $sessionId
                );
            } else {
                $row['PorcentajeAsistencia'] = '';
            }
        }

        return $results;
    }

    /**
     * Extract session ID from a participant code.
     * Expected format: [prefix]-[middle]-[sessionId].
     */
    private function extractSessionIdFromParticipantCode(string $participantCode): int
    {
        $codeParts = explode(self::CODE_SEPARATOR, $participantCode);

        if (\count($codeParts) < self::MIN_CODE_PARTS) {
            return 0;
        }

        $sessionId = (int) end($codeParts);

        return $sessionId > 0 ? $sessionId : 0;
    }

    /**
     * Validate if a participant code has the expected format.
     */
    private function isValidParticipantCode(string $participantCode): bool
    {
        $codeParts = explode(self::CODE_SEPARATOR, $participantCode);

        return \count($codeParts) >= self::MIN_CODE_PARTS && !empty(end($codeParts));
    }

    private function extractUserIdFromRow(array $row): int
    {
        // Try to get user ID from CodEmpleado or use a query to find it by email
        if (!empty($row['CorreoElectronico'])) {
            $user = $this->getEntityManager()
                ->getRepository(User::class)
                ->findOneBy(['email' => $row['CorreoElectronico']]);

            return $user ? $user->getId() : 0;
        }

        return 0;
    }

    private function getAssistancePercentForUserInSession(int $userId, int $sessionId): int
    {
        $session = $this->getEntityManager()
            ->getRepository(AnnouncementGroupSession::class)
            ->find($sessionId);

        if (!$session) {
            return 0;
        }

        $assistance = $session->getAssistance() ?? [];

        foreach ($assistance as $record) {
            if (isset($record['id']) && (int) $record['id'] === $userId) {
                if (isset($record['assistance']) && true === $record['assistance']) {
                    return isset($record['percent']) ? (int) $record['percent'] : 100;
                }

                return 0;
            }
        }

        return 0;
    }

    public function findAnnouncementsByCourseForUser(Course $course, User $user): array
    {
        $isAdmin = $user->isAdmin();
        $isManager = $user->isManager() || $user->isManagerEditor();

        if (!$isAdmin && !$isManager) {
            return [];
        }

        $qb = $this->createQueryBuilder('a')
            ->select('a')
            ->innerJoin('a.course', 'c')
            ->where('c = :course')
            ->setParameter('course', $course);

        if ($isManager && !$isAdmin) {
            $orX = $qb->expr()->orX(
                $qb->expr()->eq('a.createdBy', ':user')
            );

            if ($this->settings->get('app.announcement.managers.sharing')) {
                $qb->leftJoin('a.announcementManagers', 'm');
                $orX->add($qb->expr()->eq('m.manager', ':user'));
            }

            $qb->andWhere($orX)
                ->setParameter('user', $user);
        }

        $qb->orderBy('a.id', 'DESC');

        return $qb->getQuery()->getResult();
    }
}
