<?php

declare(strict_types=1);

namespace App\Modules\Announcement\Controller;

use App\Admin\Traits\DeleteFilePathTrait;
use App\Admin\Traits\FileValidationTrait;
use App\Admin\Traits\SerializerTrait;
use App\Admin\Traits\UserManagerTrait;
use App\Entity\AlertTypeTutor;
use App\Entity\Announcement;
use App\Entity\AnnouncementAlertTutor;
use App\Entity\AnnouncementAprovedCriteria;
use App\Entity\AnnouncementConfiguration;
use App\Entity\AnnouncementConfigurationType;
use App\Entity\AnnouncementCriteria;
use App\Entity\AnnouncementCriteriaTranslation;
use App\Entity\AnnouncementGroup;
use App\Entity\AnnouncementGroupSession;
use App\Entity\AnnouncementModality;
use App\Entity\AnnouncementTemporalization;
use App\Entity\AnnouncementTutor;
use App\Entity\Classroomvirtual;
use App\Entity\ClassroomvirtualType;
use App\Entity\Course;
use App\Entity\CourseCategory;
use App\Entity\CourseCategoryTranslation;
use App\Entity\FilesManager;
use App\Entity\GenericToken;
use App\Entity\Survey;
use App\Entity\SurveyAnnouncement;
use App\Entity\TypeCourse;
use App\Entity\TypeCourseAnnouncementStepCreation;
use App\Entity\TypeDiploma;
use App\Entity\TypeIdentification;
use App\Entity\TypeMoney;
use App\Entity\User;
use App\Entity\UserCompany;
use App\Entity\UserFieldsFundae;
use App\Enum\TypeCourse as EnumTypeCourse;
use App\Enum\TypeSession;
use App\Repository\AnnouncementRepository;
use App\Repository\UserRepository;
use App\Service\Annoucement\Admin\AnnouncementConfigurationsService;
use App\Service\Annoucement\Admin\AnnouncementService;
use App\Service\Annoucement\Admin\ExternalAnnouncementImportService;
use App\Service\Annoucement\Admin\ExternalAnnouncementParticipantImportService;
use App\Service\Annoucement\ReportPdf\ExcelReportGeneratorService;
use App\Service\Announcement\AnnouncementExtraService;
use App\Service\FilesManager\FilesManagerService;
use App\Service\SettingsService;
use App\Service\VirtualClass\ClassroomvirtualService;
use App\V2\Application\Command\CreateVirtualMeeting;
use App\V2\Application\Command\DeleteVirtualMeeting;
use App\V2\Application\Command\UpdateVirtualMeeting;
use App\V2\Application\Query\GetVirtualMeetings;
use App\V2\Application\Service\Announcement\AnnouncementAuthorizationServiceInterface;
use App\V2\Domain\Announcement\Exception\ManagerNotAuthorizedException;
use App\V2\Domain\Bus\CommandBus;
use App\V2\Domain\Bus\QueryBus;
use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Criteria\CriteriaException;
use App\V2\Domain\Shared\Uuid\InvalidUuidException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Domain\Shared\Uuid\UuidCollection;
use App\V2\Domain\VirtualMeeting\PaginatedVirtualMeetingCollection;
use App\V2\Domain\VirtualMeeting\VirtualMeetingCollection;
use App\V2\Domain\VirtualMeeting\VirtualMeetingCriteria;
use App\V2\Infrastructure\Shared\DateTimeTransformer;
use App\V2\Infrastructure\Validator\Admin\VirtualMeetingValidator;
use App\V2\Infrastructure\Validator\ValidatorException;
use App\V2\Infrastructure\VirtualMeeting\VirtualMeetingDTOTransformer;
use App\V2\Infrastructure\VirtualMeeting\VirtualMeetingTransformer;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\NonUniqueResultException;
use FOS\RestBundle\Controller\Annotations as Rest;
use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Reader\Exception;
use PhpOffice\PhpSpreadsheet\Shared\Date;
use Psr\Log\LoggerInterface;
use Sensio\Bundle\FrameworkExtraBundle\Configuration\IsGranted;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\BinaryFileResponse;
use Symfony\Component\HttpFoundation\File\Exception\FileException;
use Symfony\Component\HttpFoundation\File\UploadedFile;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\KernelInterface;
use Symfony\Component\PasswordHasher\Hasher\UserPasswordHasherInterface;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\Validator\Constraints as Assert;
use Symfony\Component\Validator\Validator\ValidatorInterface;
use Symfony\Contracts\Translation\TranslatorInterface;

/**
 * All announcement date actions will be handled in UTC
 * new DateTime() -> will be using UTC
 * with user provided DateTime, use the specified timezone and convert into UTC.
 *
 * @Rest\Route("/admin/announcement/form")
 */
class AnnouncementFormController extends AbstractController
{
    use SerializerTrait;
    use FileValidationTrait;
    use DeleteFilePathTrait;
    use UserManagerTrait;

    private EntityManagerInterface $em;
    private SettingsService $settings;
    private \DateTimeZone $utcDateTimeZone;
    private AnnouncementConfigurationsService $announcementConfigurationsService;
    protected LoggerInterface $logger;
    protected TranslatorInterface $translator;

    private const DEFAULT_TYPE_COURSE = TypeCourse::CODE_ONSITE;

    private ExcelReportGeneratorService $excelReportGeneratorService;
    private AnnouncementService $announcementService;
    private ExternalAnnouncementParticipantImportService $externalAnnouncementParticipantImportService;
    private ExternalAnnouncementImportService $externalAnnouncementImportService;
    private Security $security;
    private AnnouncementExtraService $announcementExtraService;

    public function __construct(
        EntityManagerInterface $em,
        SettingsService $settings,
        AnnouncementConfigurationsService $announcementConfigurationsService,
        TranslatorInterface $translator,
        ExcelReportGeneratorService $excelReportGeneratorService,
        AnnouncementService $announcementService,
        ExternalAnnouncementParticipantImportService $externalAnnouncementParticipantImportService,
        ExternalAnnouncementImportService $externalAnnouncementImportService,
        Security $security,
        LoggerInterface $logger,
        AnnouncementExtraService $announcementExtraService,
        private readonly AnnouncementAuthorizationServiceInterface $announcementAuthorizationService,
        private readonly CommandBus $commandBus,
        private readonly QueryBus $queryBus,
    ) {
        $this->em = $em;
        $this->settings = $settings;
        $this->utcDateTimeZone = new \DateTimeZone('UTC');
        $this->announcementConfigurationsService = $announcementConfigurationsService;
        $this->translator = $translator;

        $this->excelReportGeneratorService = $excelReportGeneratorService;
        $this->announcementService = $announcementService;
        $this->externalAnnouncementParticipantImportService = $externalAnnouncementParticipantImportService;
        $this->externalAnnouncementImportService = $externalAnnouncementImportService;
        $this->security = $security;
        $this->logger = $logger;
        $this->announcementExtraService = $announcementExtraService;
    }

    /**
     * @Rest\Get("/courses")
     */
    public function findCourses(Request $request): Response
    {
        try {
            $query = $request->get('query');
            $typeCourse = $request->get('typeCourse');
            $page = $request->get('page', 1);
            $pageSize = $request->get('page-size', 10);
            $coursesStatus = $request->get('coursesStatus');
            $typeDenomination = null;

            /** @var User $user */
            $user = $this->getUser();
            /** @var Course[] $courses */
            $paramsToPaginated = ['query' => $query, 'typeCourse' => $typeCourse, 'page' => $page, 'pageSize' => $pageSize, 'coursesStatus' => $coursesStatus];
            $data = $this->em->getRepository(Course::class)->findCoursesByPagination($paramsToPaginated, $user, $typeDenomination);
            $courses = $data['courses'];
            $totalItems = $data['totalItems'];

            $data = [];
            $result = [];
            foreach ($courses as $course) {
                if (EnumTypeCourse::EXTERN === $typeDenomination || $course->isCompleted()) {
                    $typeCourse = $course->getTypeCourse();

                    $data = [
                        'id' => $course->getId(),
                        'code' => $course->getCode(),
                        'name' => $course->getName(),
                        'image' => $this->settings->get('app.course_uploads_path') . DIRECTORY_SEPARATOR . $course->getImage(),
                        'thumbnailUrl' => $course->getThumbnailUrl(),
                    ];

                    if ($typeCourse) {
                        $data['typeCourseId'] = $typeCourse->getId();
                    }

                    $result[] = $data;
                }
            }

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [
                    'items' => $result,
                    'totalItems' => (int) $totalItems,
                ],
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    private function generateActionCode()
    {
        $actionCode = 'F18';
        $number = \strval(rand(0, 9999));
        $padding = \strval(rand(0, 9));
        $l1 = \chr(rand(65, 90));
        $l2 = \chr(rand(65, 90));
        $fullCode = $actionCode . str_pad($number, 4, $padding, STR_PAD_LEFT) . $l1 . $l2;

        $announcement = $this->em->getRepository(Announcement::class)->findOneBy(['actionCode' => $fullCode]);
        if ($announcement) {
            return false;
        }

        return $fullCode;
    }

    private function generateAnnouncementCode()
    {
        $code = bin2hex(random_bytes(8));
        $savedCode = $this->em->getRepository(Announcement::class)->findOneBy(['code' => $code]);
        if ($savedCode) {
            return false;
        }

        return $code;
    }

    /**
     * @Rest\Get("/pre-data")
     */
    public function loadDataCatalog(Request $request): Response
    {
        try {
            $source = $request->get('source', EnumTypeCourse::INTERN);
            $source = strtoupper($source);

            /** Generate codes */
            $announcementCode = false;
            $actionCode = false;
            /** @var User $user */
            $user = $this->getUser();

            while (false === $announcementCode) {
                $announcementCode = $this->generateAnnouncementCode();
            }
            while (false === $actionCode) {
                $actionCode = $this->generateActionCode();
            }

            $criteria = [];

            foreach ($this->em->getRepository(AnnouncementCriteria::class)->findBy(['active' => true], ['id' => 'ASC']) as $c) {
                /** @var AnnouncementCriteriaTranslation $translation */
                $translation = $c->translate($user->getLocale(), false);
                $name = $translation->getName();
                $description = $translation->getDescription();
                $criteria[] = [
                    'id' => $c->getId(),
                    'name' => $name ?? $c->getName(),
                    'description' => $description ?? $c->getDescription(),
                    'extra' => $c->getExtra(),
                ];
            }
            $appTimezones = $this->settings->get('app.timezones');

            $timezones = \is_array($appTimezones) ? $appTimezones : json_decode($appTimezones, true) ?? [];
            sort($timezones);

            $stepsConfigurations = $this->em
                ->getRepository(TypeCourseAnnouncementStepCreation::class)
                ->getStepsOfCourseInAnnouncement($source, $user->getLocale());

            $data = [
                'typeCourses' => $this->em
                    ->getRepository(TypeCourse::class)
                    ->getActiveTranslatedListAndConfigurations($user->getLocale(), $source),
                'approvedCriteria' => $criteria,
                'clientConfigurationAnnouncement' => $this->em->getRepository(AnnouncementConfigurationType::class)->getConfigurationAnnouncement(),
                'alertTypeTutor' => $this->em
                    ->getRepository(AlertTypeTutor::class)
                    ->getActiveTranslatedList($user->getLocale()),
                'typeDiplomas' => $this->em->getRepository(TypeDiploma::class)->findBy(['active' => true]),
                'typeSurveys' => $this->em->getRepository(Survey::class)->findBy(['active' => true, 'applyTo' => 3]),
                'fundaeConfiguration' => [
                    'max_students_per_group' => $this->settings->get('app.fundae.max_students_per_group'),
                    'action_types' => $this->settings->get('app.fundae.action_types'),
                    'min_passing_score' => $this->settings->get('app.fundae.min_passing_score'),
                    'default_entry_margin' => (int) $this->settings->get('app.fundae.default_entry_margin'),
                    'default_exit_margin' => (int) $this->settings->get('app.fundae.default_exit_margin'),
                ],
                'classroomvirtualTypes' => $this->em
                    ->getRepository(ClassroomvirtualType::class)
                    ->findBy(['state' => true]),
                'codes' => [
                    'code' => $announcementCode,
                    'actionCode' => $actionCode,
                ],
                'timezones' => $timezones,
                'typeMoneys' => $this->em->getRepository(TypeMoney::class)->findBy(['state' => true]),
                'typeSessions' => [TypeSession::TYPE_PRESENTIAL, TypeSession::TYPE_VIRTUAL],
                'stepsConfigurations' => $stepsConfigurations,
                'enrollmentTemplate' => $this->announcementConfigurationsService->hasEnrollmentTemplate() ? 'plantilla_matricula.xls' : 'plantilla_registro.xls',
                'typeIdentifications' => $this->em->getRepository(TypeIdentification::class)->getTypesIdentification(),
                'mainIdentification' => $this->em->getRepository(TypeIdentification::class)->getMainIdentificationForThePlatform($this->getUser()->getLocale()),
                'modalities' => $this->em->getRepository(AnnouncementModality::class)->getAnnouncementModalities($this->getUser()->getLocale()),
                'companies' => $this->em->getRepository(UserCompany::class)->findBy(['state' => true], ['name' => 'ASC']),
                'extra' => $this->announcementExtraService->getActiveExtraData($user->getLocale()),
            ];

            if (EnumTypeCourse::EXTERN === $source) {
                $categories = [];
                foreach ($this->em->getRepository(CourseCategory::class)->findAll() as $courseCategory) {
                    /** @var CourseCategoryTranslation|null $translation */
                    $translation = $courseCategory->translate($user->getLocale(), false);
                    $categories[] = [
                        'id' => $courseCategory->getId(),
                        'name' => $translation && !empty($translation->getName()) ? $translation->getName() : $courseCategory->getName(),
                    ];
                }
                $data['courseCategories'] = $categories;
            }

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => $data,
            ], ['groups' => ['list']]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
                'trace' => $e->getTrace(),
            ]);
        }
    }

    /**
     * @Rest\Get("/tutors")
     */
    public function loadTutors(FilesManagerService $filesManagerService): Response
    {
        /** @var User[] $tutors */
        $userTutors = $this->em->getRepository(User::class)->loadTutors('fromForm');

        $data = [];
        /** @var User $user */
        foreach ($userTutors as $user) {
            $u = [
                'id' => $user->getId(),
                'name' => $user->getFullName(),
                'cv' => [],
            ];
            $userFieldsFundae = $user->getUserFieldsFundae();
            if ($userFieldsFundae) {
                $cv = $userFieldsFundae->getCvFilesManager();
                if ($cv) {
                    $u['cv'] = [
                        'name' => $cv->getOriginalName(),
                        'filename' => $cv->getFilename(),
                        'url' => $filesManagerService->getUrl($cv),
                        'source' => FilesManager::TYPE_FILES_MANAGER,
                    ];
                }
                $u['dni'] = $userFieldsFundae->getDni();
                $u['telephone'] = $userFieldsFundae->getTelephone();
                $u['email'] = $user->getEmail();
                $u['company'] = $userFieldsFundae->getUserCompany() ? [
                    'id' => $userFieldsFundae->getUserCompany()->getId(),
                    'name' => $userFieldsFundae->getUserCompany()->getName(),
                ] : null;
            }

            $data[] = $u;
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $data,
        ]);
    }

    /**
     * @Rest\Post("/available-students", name="available_students")
     */
    public function availableUsers(Request $request): Response
    {
        try {
            $announcementId = (int) $request->get('announcement');

            // Validate that announcement ID is provided
            if ($announcementId <= 0) {
                return $this->sendResponse([
                    'status' => Response::HTTP_BAD_REQUEST,
                    'error' => true,
                    'data' => 'The "announcement" parameter is required and must be a valid ID.',
                ]);
            }

            // Get announcement data
            $announcement = $this->em->getRepository(Announcement::class)->find($announcementId);
            if (!$announcement) {
                return $this->sendResponse([
                    'status' => Response::HTTP_NOT_FOUND,
                    'error' => true,
                    'data' => 'Announcement not found with ID: ' . $announcementId,
                ]);
            }

            // Get dates directly from announcement (already in UTC)
            $startAt = $announcement->getStartAt();
            $finishAt = $announcement->getFinishAt();

            if (!$startAt || !$finishAt) {
                return $this->sendResponse([
                    'status' => Response::HTTP_BAD_REQUEST,
                    'error' => true,
                    'data' => 'The announcement does not have valid dates configured.',
                ]);
            }

            $page = $request->get('page');
            $pageSize = $request->get('page-size');
            if (!$page || !$pageSize) {
                return $this->sendResponse([
                    'status' => Response::HTTP_BAD_REQUEST,
                    'error' => true,
                    'data' => 'Invalid page or page-size parameters.',
                ]);
            }

            $courseId = $announcement->getCourse()->getId();
            $course = $this->em->getRepository(Course::class)->find($courseId);

            if (!$course) {
                return $this->sendResponse([
                    'status' => Response::HTTP_NOT_FOUND,
                    'error' => true,
                    'data' => 'Course not found with ID: ' . $courseId,
                ]);
            }

            // Always get duplicated users for all announcement types
            $duplicatedUsersIds = $this->findUsersIdsCalledInAnnouncements(
                $courseId,
                $announcementId,
                $startAt,
                $finishAt
            );

            /**
             * Do user specific queries $data.
             */
            $data = json_decode($request->getContent(), true);
            $searchQuery = $data['searchQuery'] ?? null;
            $filters = $data['filters'] ?? [];
            $statusFilters = $data['statusFilters'] ?? [];

            $statusFilter = User::ACTIVITY_STATUS_FILTER[User::ALL_USERS];
            if (isset($statusFilters['Usuarios']['id']) && null !== $statusFilters['Usuarios']['id']) {
                $statusFilter = $statusFilters['Usuarios']['id'];
            }
            /** @var User $user */
            $user = $this->getUser();

            if ($user->isManager() && !$user->isAdmin()) {
                if (0 === $user->getFilters()->count()) {
                    return $this->sendResponse([
                        'status' => Response::HTTP_OK,
                        'error' => false,
                        'data' => ['data' => [], 'totalItems' => 0],
                    ]);
                }
                if (empty($filters)) {
                    foreach ($user->getFilters() as $filter) {
                        $categoryName = $filter->getFilterCategory()->getName();

                        if (!isset($filters[$categoryName])) {
                            $filters[$categoryName] = [];
                        }

                        $filters[$categoryName][] = ['id' => $filter->getId()];
                    }
                } else {
                    // Check if manager can manage the selected filters.
                    if (!$this->ensureManagerCanManageFilters($user, $filters)) {
                        return $this->sendResponse([
                            'status' => Response::HTTP_FORBIDDEN,
                            'error' => true,
                            'data' => 'User does not have permission to manage the selected filters.',
                        ]);
                    }
                }
            }

            /** @var UserRepository $userRepository */
            $userRepository = $this->em->getRepository(User::class);

            $qb = $userRepository->getFilteredUsers(
                user: $user,
                filters: $filters,
                excludeUsersId: [],
                searchQuery: $searchQuery,
                returnQuery: true,
                userActiveStatus: $statusFilter
            );

            $result = $this->em->getRepository(User::class)->getPaginatedAvailablesUsers((int) $page, (int) $pageSize, $qb);

            $resultData = $result['data'];
            foreach ($resultData as $key => $userData) {
                $resultData[$key]['isDuplicated'] = \in_array($userData['id'], $duplicatedUsersIds);
            }

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [
                    'totalItems' => (int) $result['totalItems'],
                    'data' => $resultData,
                ],
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_BAD_REQUEST,
                'error' => true,
                'data' => 'Invalid "course" or "announcement" parameters.',
            ]);
        }
    }

    private function findUsersIdsCalledInAnnouncements(
        int $courseId,
        int $announcementId,
        \DateTimeInterface $startAt,
        \DateTimeInterface $finishAt
    ): array {
        $qb = $this->em
            ->getRepository(Announcement::class)
            ->createQueryBuilder('a');

        $qb->select('DISTINCT(u.id) as userId')
            ->join('a.called', 'cal')
            ->join('cal.user', 'u')
            ->where('a.course = :course')
            ->andWhere('a.id != :announcement_id')
            ->andWhere('
                (a.startAt <= :start_at AND a.finishAt >= :start_at)
                OR (a.startAt <= :finish_at AND a.finishAt >= :finish_at)
                OR (a.startAt <= :start_at AND a.finishAt >= :finish_at)
                OR (a.startAt >= :start_at AND a.finishAt <= :finish_at)
            ')
            ->andWhere('a.status IN (:statuses)')
            ->setParameters([
                'course' => $courseId,
                'announcement_id' => $announcementId,
                'start_at' => $startAt,
                'finish_at' => $finishAt,
                'statuses' => [
                    Announcement::STATUS_CONFIGURATION,
                    Announcement::STATUS_ACTIVE,
                ],
            ]);

        $results = $qb->getQuery()->getArrayResult();

        return array_column($results, 'userId');
    }

    /**
     * @Rest\Get("/announcement/{id}")
     *
     * @throws CollectionException
     * @throws CriteriaException
     * @throws InvalidUuidException
     * @throws NonUniqueResultException
     */
    public function getAnnouncement(
        Request $request,
        UserPasswordHasherInterface $userPasswordHasher,
        Announcement $announcement
    ): Response {
        /** @var AnnouncementRepository $announcementRepository */
        $announcementRepository = $this->em->getRepository(Announcement::class);

        /** @var User $user */
        $user = $this->getUser();

        $course = $announcement->getCourse();
        $type = $course->getTypeCourse();

        $baseUrl = $request->getSchemeAndHttpHost();

        /** @var AnnouncementCriteria[] $announcementCriteria */
        $announcementCriteria = $this->em->getRepository(AnnouncementCriteria::class)->findBy([
            'active' => true,
        ], ['id' => 'ASC']);

        $criteriaValues = $this->em->getRepository(AnnouncementAprovedCriteria::class)->createQueryBuilder('a')
            ->select('a.id', 'a.value', 'a.extra')
            ->addSelect('c.id as criteriaId')
            ->join('a.announcementCriteria', 'c')
            ->where('a.announcement =:announcement')->setParameter('announcement', $announcement)
            ->getQuery()
            ->getResult();
        $criteria = [];
        foreach ($announcementCriteria as $c) {
            $criteria[$c->getId()] = [
                'enabled' => false,
                'value' => 0,
                'dataType' => $c->getExtra()['dataType'],
                'alwaysEnabled' => $c->getExtra()['alwaysEnabled'] ?? false,
            ];
        }

        foreach ($criteriaValues as $v) {
            $criteria[$v['criteriaId']]['enabled'] = true;
            $criteria[$v['criteriaId']]['value'] = (float) $v['value'];
        }

        // Enabled configurations
        /** @var AnnouncementConfigurationType[] $confTypes */
        $selectedConfTypes = $this->em->getRepository(AnnouncementConfigurationType::class)->createQueryBuilder('c')
            ->select('c')
            ->join('c.announcementConfigurations', 'ac')
            ->where('ac.announcement =:announcement')
            ->setParameter('announcement', $announcement)
            ->getQuery()
            ->getResult();

        $configurations = $this->em->getRepository(AnnouncementConfigurationType::class)->findBy([
            'active' => true,
        ]);

        $announcementConfiguration = [];
        foreach ($configurations as $conf) {
            $announcementConfiguration['configuration-' . $conf->getId()] = false;
        }

        foreach ($selectedConfTypes as $c) {
            $announcementConfiguration['configuration-' . $c->getId()] = true;
        }

        $survey = $this->em->getRepository(SurveyAnnouncement::class)->findOneBy(['announcement' => $announcement]);

        // Alerts
        /** @var AlertTypeTutor[] $alertTypes */
        $alertTypes = $this->em->getRepository(AlertTypeTutor::class)->findBy(['active' => true]);
        $alerts = [];
        foreach ($alertTypes as $t) {
            $alerts['alert-' . $t->getId()] = false;
        }

        /** @var AnnouncementTutor $tutor */
        $tutor = $this->em->getRepository(AnnouncementTutor::class)->createQueryBuilder('t')
            ->where('t.announcement =:announcement')
            ->setParameter('announcement', $announcement)
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();

        if ($tutor) {
            foreach ($tutor->getAnnouncementAlertTutors() as $a) {
                $alerts['alert-' . $a->getAlertTypeTutor()->getId()] = true;
            }
        }

        $didacticGuide = $announcement->getDidaticGuide();

        $timing = [];
        foreach ($announcementRepository->getChapterTiming($announcement) as $t) {
            $timing[$t['id']] = $t;
            // Convert to string
            $timeInSeconds = $t['minimumTime'];
            $hours = floor($timeInSeconds / 3600);
            $timeInSeconds -= $hours * 3600;
            $minutes = floor($timeInSeconds / 60);
            $seconds = $timeInSeconds - ($minutes * 60);
            $timing[$t['id']]['time'] = str_pad("$hours", 2, '0', STR_PAD_LEFT) . ':' . str_pad("$minutes", 2, '0', STR_PAD_LEFT) . ':' . str_pad("$seconds", 2, '0', STR_PAD_LEFT);
        }

        $courseCategory = $course->getCategory();

        $filteredExtraSections = $this->announcementExtraService->filterExtraSections(
            $announcement->getExtra(),
            $this->announcementExtraService->getAllowedExtraSections()
        );
        $extraData = $this->announcementExtraService->getActiveExtraData($user->getLocale());
        $extra = $this->announcementExtraService->mapFilteredExtraSections($filteredExtraSections, $extraData, false);

        $students = $announcementRepository->getAnnouncementStudentsWithGroup($announcement);
        $students = $this->hidrateWithVirtualMeetingData($students);

        $announcementData = [
            'id' => $announcement->getId(),
            'notifiedAt' => $announcement->getNotifiedAt(),
            'code' => $announcement->getCode(),
            'course' => [
                'id' => $course->getId(),
                'code' => $course->getCode(),
                'image' => $course->getImage(),
                'name' => $course->getName(),
                'locale' => $course->getLocale(),
                'typeCourseId' => $type->getId(),
                'type' => $type->getType(),

                'category' => [
                    'id' => $courseCategory->getId(),
                    'name' => $courseCategory->getName(),
                ],
            ],
            'startAt' => $announcement->getStartAt()->format('c'),
            'finishAt' => $announcement->getFinishAt()->format('c'),
            'totalHours' => $announcement->getTotalHours(),
            'extra' => $extra,
            'usersPerGroup' => (int) $announcement->getUsersPerGroup(),
            'didacticGuide' => $didacticGuide ? [
                'name' => $didacticGuide->getOriginalName(),
                'filename' => $this->settings->get('app.announcement_didactic_guide') . $didacticGuide->getFilename(),
            ] : null, // Required to handle the file,
            'objectiveAndContent' => $announcement->getObjectiveAndContents(),
            'chapterTiming' => $timing,
            'configAnnouncement' => $announcementConfiguration,
            'status' => $announcement->getStatus() ?? Announcement::STATUS_CONFIGURATION,

            'type' => $type ? $type->getType() : TypeCourse::CODE_ONLINE,
            'students' => $students,

            'subsidized' => $announcement->getSubsidized(),
            'actionType' => $announcement->getActionType(),
            'actionCode' => $announcement->getActionCode(),
            'denomination' => $announcement->getDenomination(),
            'contactPerson' => $announcement->getContactPerson(),
            'contactPersonEmail' => $announcement->getContactPersonEmail(),
            'contactPersonTelephone' => $announcement->getContactPersonTelephone(),

            'inspectorAccess' => $announcementRepository->generateAnnouncementUrl($userPasswordHasher, $this->getUser(), $announcement, $baseUrl),

            'typeDiploma' => ($announcementTypeDiploma = $announcement->getTypeDiploma()) ? $announcementTypeDiploma->getId() : null,
            'isConfirmationRequiredDiploma' => $announcement->isIsConfirmationRequiredDiploma(),
            'typeSurvey' => $survey ? $survey->getSurvey()->getId() : null,

            'approvedCriteriaValues' => $criteria,
            'alertTypeTutorValues' => $alerts,
            'timezone' => $announcement->getTimezone(),
            'source' => $type->getDenomination(),
        ];

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'data' => $announcementData,
        ], [
            'datetime_format' => 'c',
        ]);
    }

    /**
     * @throws CriteriaException
     * @throws CollectionException
     * @throws InvalidUuidException
     */
    private function hidrateWithVirtualMeetingData(array $studentsArray): array
    {
        $virtualMeetingIds = [];

        foreach ($studentsArray as $group) {
            foreach ($group['sessions'] as $session) {
                if (null !== $session['virtual_meeting_id']) {
                    $virtualMeetingIds[] = $session['virtual_meeting_id'];
                }
            }
        }

        $virtualMeetingIds = array_unique($virtualMeetingIds);

        if (empty($virtualMeetingIds)) {
            return $studentsArray;
        }

        $virtualMeetingIdCollection = new UuidCollection(
            array_map(
                fn ($id) => new Uuid($id),
                $virtualMeetingIds,
            )
        );

        /** @var PaginatedVirtualMeetingCollection $paginatedVirtualMeetingCollection */
        $paginatedVirtualMeetingCollection = $this->queryBus->ask(
            new GetVirtualMeetings(
                criteria: VirtualMeetingCriteria::createByIds($virtualMeetingIdCollection)
            )
        );

        if (0 === $paginatedVirtualMeetingCollection->getTotalItems()) {
            return $studentsArray;
        }

        /** @var VirtualMeetingCollection $virtualMeetings */
        $virtualMeetings = $paginatedVirtualMeetingCollection->getCollection();
        $virtualMeetingsById = $virtualMeetings->allIndexedById();

        foreach ($studentsArray as $groupIndex => $group) {
            foreach ($group['sessions'] as $sessionIndex => $session) {
                if (null !== $session['virtual_meeting_id']) {
                    if (isset($virtualMeetingsById[$session['virtual_meeting_id']])) {
                        $studentsArray[$groupIndex]['sessions'][$sessionIndex]['virtual_meeting']
                            = VirtualMeetingTransformer::fromVirtualMeetingToArray(
                                virtualMeeting: $virtualMeetingsById[$session['virtual_meeting_id']]
                            );
                    }
                }
            }
        }

        return $studentsArray;
    }

    /**
     * @Rest\Get("/pre-selected-course")
     */
    public function getPreSelectedCourse(Request $request): Response
    {
        $courseId = $request->get('courseId');
        if (empty($courseId)) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'No course ID',
            ]);
        }
        $course = $this->em->getRepository(Course::class)->find($courseId);
        if (!$course) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'Course not found',
            ]);
        }
        $typeCourse = $course->getTypeCourse();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [
                'id' => $course->getId(),
                'code' => $course->getCode(),
                'name' => $course->getName(),
                'image' => $course->getImage(),
                'type' => $typeCourse->getType(),
            ],
        ]);
    }

    /**
     * @Rest\Get("/chapters/{id}")
     */
    public function getCourseChapters(Course $course): Response
    {
        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => $this->em->getRepository(AnnouncementTemporalization::class)->getCourseChapters($course),
        ]);
    }

    /**
     * @Rest\Post("/save/general-info-extern")
     *
     * @return Response
     *
     * @throws \Exception
     */
    public function saveAnnouncementGeneralInfoExtern(Request $request, AnnouncementRepository $announcementRepository)
    {
        $content = json_decode($request->getContent(), true);
        $id = $content['id'] ?? null;
        if (!empty($id)) {
            $announcement = $announcementRepository->find($id);
            if (!$announcement) {
                return $this->sendResponse([
                    'status' => Response::HTTP_OK,
                    'error' => true,
                    'data' => 'Announcement not found',
                ]);
            }
        } else {
            $announcement = new Announcement();
            $announcement->setStatus(Announcement::STATUS_CONFIGURATION);
        }

        /**
         * Handle course.
         */
        $courseData = $content['course'] ?? [];
        if (empty($courseData)) {
            return $this->sendResponse([
                'status' => Response::HTTP_BAD_REQUEST,
                'error' => true,
                'data' => 'Course information is required',
            ]);
        }

        $courseName = $courseData['name'] ?? null;
        $courseLocale = $courseData['locale'] ?? null;
        $courseCategoryId = ($courseData['category'] ?? [])['id'] ?? null;
        $courseTypeCourseId = $courseData['typeCourseId'] ?? null;

        if (empty($courseName) || empty($courseLocale) || empty($courseCategoryId) || empty($courseTypeCourseId)) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'Course[name, locale, category, typeCourseId] required',
            ]);
        }

        $courseCategory = $this->em->getRepository(CourseCategory::class)->find($courseCategoryId);
        if (!$courseCategory) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'typeCourseId not found',
            ]);
        }

        $typeCourse = $this->em->getRepository(TypeCourse::class)->find($courseTypeCourseId);
        if (!$typeCourse) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'typeCourseId not found',
            ]);
        }
        $course = $this->em->getRepository(Course::class)->findOneBy([
            'name' => $courseName,
            'category' => $courseCategory,
            'typeCourse' => $typeCourse,
        ]);

        if (!$course) {
            $course = new Course();
            $course->setCode($courseName)
                ->setName($courseName)
                ->setTypeCourse($typeCourse)
                ->setCategory($courseCategory)
                ->setLocale($courseLocale)
                ->setDescription('Extern course')
                ->setOpen(false)
                ->setIsNew(false);
            $this->em->persist($course);
        }

        $announcementCode = $content['code'] ?? null;
        $announcementStartAt = $content['startAt'] ?? null;
        $announcementFinishAt = $content['finishAt'] ?? null;
        $announcementTimezone = $content['timezone'] ?? null;
        $announcementTotalHours = (float) $content['totalHours'] ?? 0;
        $announcementExtra = $content['extra'] ?? null;

        if (
            empty($announcementCode)
            || empty($announcementStartAt)
            || empty($announcementFinishAt)
            || empty($announcementTimezone)
            || empty($announcementTotalHours)
        ) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'Fields required: [code, startAt, finishAt, timezone, totalHours]',
            ]);
        }

        $tz = new \DateTimeZone($announcementTimezone);
        $startAt = new \DateTimeImmutable($announcementStartAt, $tz);
        $finishAt = new \DateTimeImmutable($announcementFinishAt, $tz);

        if ($finishAt < $startAt) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => ['ANNOUNCEMENT.FORM.ENTITY.INVALID_START_AT_FINISH_AT'],
            ]);
        }

        $announcement->setCourse($course)
            ->setCode($announcementCode)
            ->setTimezone($announcementTimezone)
            ->setStartAt($startAt)
            ->setFinishAt($finishAt)
            ->setTotalHours($announcementTotalHours)
            ->setUsersPerGroup(999);

        if (!empty($announcementExtra)) {
            $existingExtra = $announcement->getExtra();
            if (!\is_array($existingExtra)) {
                $existingExtra = [];
            }
            foreach ($announcementExtra as $key => $value) {
                $existingExtra[$key] = $value;
            }
            $announcement->setExtra($existingExtra);
        }

        /**
         * Fill announcement group info
         * source: $content['students'].
         */
        $studentsData = $content['students'] ?? [];
        if (empty($studentsData)) {
            return $this->sendResponse([
                'status' => Response::HTTP_BAD_REQUEST,
                'error' => true,
                'data' => 'Students must not be empty',
            ]);
        }

        $place = $studentsData[0]['place'] ?? null;
        $cost = $studentsData[0]['cost'] ?? 0;
        $typeMoneyId = $studentsData[0]['typeMoney']['id'];

        $firstAnnouncementGroup = $this->em->getRepository(AnnouncementGroup::class)->findOneBy([
            'announcement' => $announcement,
            'groupNumber' => 1,
        ]);

        if (!$firstAnnouncementGroup) {
            $firstAnnouncementGroup = new AnnouncementGroup();
            $firstAnnouncementGroup->setGroupNumber(1)
                ->setAnnouncement($announcement);
        }
        $typeMoney = $this->em->getRepository(TypeMoney::class)->find($typeMoneyId);
        $firstAnnouncementGroup->setPlace($place)
            ->setCost((string) $cost)
            ->setTypeMoney($typeMoney);

        $userTutor = $this->em->getRepository(User::class)->getDefaultExternUser();
        $announcementTutor = $firstAnnouncementGroup->getAnnouncementTutor();
        if (!$announcementTutor) {
            $announcementTutor = new AnnouncementTutor();
            $announcementTutor->setTutor($userTutor)
                ->setAnnouncement($announcement)
                ->setAnnouncementGroup($firstAnnouncementGroup)
                ->setFilename('INVALID_FILE');
        }

        $tutorData = $studentsData[0]['tutor'] ?? [];
        $tutorName = $tutorData['name'] ?? null;
        $tutorEmail = $tutorData['email'] ?? null;

        if (empty($tutorName) || empty($tutorEmail)) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'tutor[name, email] required',
            ]);
        }

        $announcementTutor->setName($tutorName)
            ->setEmail($tutorEmail);

        $this->em->persist($announcement);
        $this->em->persist($firstAnnouncementGroup);
        $this->em->persist($announcementTutor);
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_CREATED,
            'error' => false,
            'data' => [
                'id' => $announcement->getId(),
                'firstGroupId' => $firstAnnouncementGroup->getId(),
                'usersPerGroup' => $announcement->getUsersPerGroup(),
            ],
        ]);
    }

    /**
     * @Rest\Post("/general-info")
     */
    public function saveAnnouncementBasicInfo(
        Request $request,
        UserPasswordHasherInterface $userPasswordHasher
    ): Response {
        $courseId = $request->get('courseId');
        if (empty($courseId)) {
            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => true,
                'data' => [
                    'Course ID is required',
                ],
            ]);
        }

        $course = $this->em->getRepository(Course::class)->find($courseId);
        if (!$course) {
            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => true,
                'data' => [
                    'Course not found',
                ],
            ]);
        }

        $announcementRepository = $this->em->getRepository(Announcement::class);

        $id = $request->get('id');
        /** @var User $user */
        $user = $this->getUser();

        if (!empty($id)) {
            $announcement = $announcementRepository->find($id);

            if (!$announcement) {
                return $this->sendResponse([
                    'status' => Response::HTTP_OK,
                    'error' => true,
                    'data' => 'Announcement not found',
                ]);
            }

            // Make sure the user have access to the announcement
            $this->announcementAuthorizationService->ensureUserCanManageAnnouncement(
                user: $user,
                announcement: $announcement,
            );

            if ($announcement->getCourse()->getId() !== $course->getId()) {
                // If the course is changed for the announcement, validate the user have permissions for the action.
                $this->announcementAuthorizationService->ensureUserCanCreateAnnouncement(
                    user: $user,
                    course: $course,
                );
            }
        } else {
            // Validate the user can create the announcement for the course.
            $this->announcementAuthorizationService->ensureUserCanCreateAnnouncement(
                user: $user,
                course: $course,
            );

            $announcement = new Announcement();
            $announcement->setStatus(Announcement::STATUS_CONFIGURATION);
        }

        $this->removeDidactidGuide($request, $announcement);

        $result = $announcementRepository->saveBasicAnnouncementInfo($announcement, $request);
        if (\is_array($result)) {
            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => true,
                'data' => $result,
            ]);
        }

        $baseUrl = $request->getSchemeAndHttpHost();

        $inspectorAccess = $announcementRepository->generateAnnouncementUrl(
            $userPasswordHasher,
            $this->getUser(),
            $announcement,
            "$baseUrl/inspector/view"
        );

        $didacticGuide = $announcement->getDidaticGuide();

        return $this->sendResponse([
            'status' => Response::HTTP_CREATED,
            'error' => false,
            'data' => [
                'id' => $announcement->getId(),
                'access' => $inspectorAccess,
                'didacticGuideResult' => $didacticGuide ? [
                    'name' => $didacticGuide->getOriginalName(),
                    'filename' => $this->settings->get('app.announcement_didactic_guide') . $didacticGuide->getFilename(),
                ] : null, // Required to handle the file,
            ],
            //            'test' => $announcement->convertToUtc(),
            'value' => (new \DateTime()),
            'valueMadrid' => (new \DateTime())->setTimezone(new \DateTimeZone($this->settings->get('app.default_timezone'))),
        ]);
    }

    private function removeDidactidGuide(Request $request, Announcement $announcement)
    {
        $didacticGuide = $request->files->get('didacticGuide');
        if ($announcement->getDidaticGuide() && $didacticGuide) {
            $this->deleteFile($this->settings->get('app.announcement_didactic_guide'), $announcement->getDidaticGuide()->getFilename());

            $this->em->remove($announcement->getDidaticGuide());
            $this->em->flush();
        }
    }

    /**
     * @Rest\Post("/bonification")
     */
    public function saveAnnouncementBonification(Request $request): Response
    {
        // TODO: Migrate to V2 CQRS handling and validation.
        $announcementRepository = $this->em->getRepository(Announcement::class);
        $id = $request->get('id');
        if (empty($id)) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'ID is required',
            ]);
        }

        $announcement = $announcementRepository->find($id);
        if (!$announcement) {
            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => true,
                'data' => 'Announcement not found',
            ]);
        }

        /** @var User $user */
        $user = $this->getUser();
        $this->announcementAuthorizationService->ensureUserCanManageAnnouncement(
            user: $user,
            announcement: $announcement,
        );

        $result = $announcementRepository->saveAnnouncementBonification($request, $announcement);

        return $this->sendResponse([
            'status' => \is_array($result) ? Response::HTTP_ACCEPTED : Response::HTTP_OK,
            'error' => \is_array($result),
            'data' => \is_array($result) ? $result : [],
        ]);
    }

    /**
     * @Rest\Post("/students")
     */
    public function saveStudents(Request $request): Response
    {
        // TODO: Migrate to V2 CQRS handling and validation.
        $announcementRepository = $this->em->getRepository(Announcement::class);
        $id = $request->get('id');
        if (empty($id)) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'ID is required',
            ]);
        }

        $announcement = $announcementRepository->find($id);
        if (!$announcement) {
            return $this->sendResponse([
                'status' => Response::HTTP_NOT_FOUND,
                'error' => true,
                'data' => 'Announcement not found',
            ]);
        }

        /** @var User $user */
        $user = $this->getUser();
        $this->announcementAuthorizationService->ensureUserCanManageAnnouncement(
            user: $user,
            announcement: $announcement,
        );

        $studentGroups = $request->get('students', []);
        $usersIds = [];

        foreach ($studentGroups as $group) {
            foreach ($group['data'] as $student) {
                $usersIds[] = $student['id'];
            }
        }

        if ($this->hasDuplicatedStudents($announcement, $usersIds)) {
            return $this->sendResponse([
                'status' => Response::HTTP_UNPROCESSABLE_ENTITY,
                'error' => true,
                'data' => 'ANNOUNCEMENT.FORM.DUPLICATED_USER',
            ]);
        }

        $result = $announcementRepository->saveStudents($request, $announcement);

        return $this->sendResponse(
            array_merge(['status' => Response::HTTP_OK], $result),
            [
                'datetime_format' => 'c',
            ]
        );
    }

    private function hasDuplicatedStudents(Announcement $announcement, array $studentsIds): bool
    {
        $course = $announcement->getCourse();
        $courseId = $course->getId();
        $duplicatedUsersIds = $this->findUsersIdsCalledInAnnouncements(
            $courseId,
            $announcement->getId(),
            $announcement->getStartAt(),
            $announcement->getFinishAt()
        );

        $userCoincidence = array_intersect($studentsIds, $duplicatedUsersIds);
        if (!empty($userCoincidence)) {
            return true;
        }

        return false;
    }

    /**
     * @Rest\Post("/group-info")
     *
     * @throws ValidatorException
     */
    public function saveGroupInformation(Request $request): Response
    {
        $announcementRepository = $this->em->getRepository(Announcement::class);
        $content = json_decode($request->getContent(), true);
        $id = $content['id'];
        $groupData = $content['data'];
        if (empty($id)) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'ID is required',
            ]);
        }

        $announcement = $announcementRepository->find($id);
        if (!$announcement) {
            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => true,
                'data' => 'Announcement not found',
            ]);
        }
        $course = $announcement->getCourse();
        $typeCourse = $course->getTypeCourse();

        foreach ($groupData as $d) {
            $group = $this->em->getRepository(AnnouncementGroup::class)->find($d['id']);
            $group->setCode($d['code']);

            $cost = $d['cost'] ?? 0;

            $group->setCost((string) $cost);
            if (isset($d['typeMoney']) && !empty($d['typeMoney'])) {
                $typeMoney = $d['typeMoney'] ?? [];
                $typeMoneyClass = $this->em->getRepository(TypeMoney::class)->find($typeMoney['id']);
                $group->setTypeMoney($typeMoneyClass);
            }
            //                    ->setDenomination($d['denomination'])
            //                    ->setCompanyProfile($d['companyProfile'])
            //                    ->setCompanyCif($d['companyCif'])
            //                    ->setFileNumber($d['fileNumber'])

            if (TypeCourse::CODE_ONLINE !== $typeCourse->getCode()) {
                $group->setNumSessions($d['numberOfSessions'])
                    ->setPlace($d['place']);

                $sessions = [];

                $prevFinishAt = null;
                $previousVirtualMeetingId = null;
                foreach ($d['sessions'] as $sInfo) {
                    if (\array_key_exists('id', $sInfo) && $sInfo['id'] > 0) {
                        $session = $this->em->getRepository(AnnouncementGroupSession::class)->find($sInfo['id']);
                        $previousVirtualMeetingId = $session->getVirtualMeetingId();
                    } else {
                        $session = new AnnouncementGroupSession();
                    }
                    $timezone = empty($sInfo['timezone']) ? $announcement->getTimezone() : $sInfo['timezone'];
                    $tz = new \DateTimeZone($timezone);
                    $start = new \DateTimeImmutable($sInfo['startAt'], $tz);
                    $finish = new \DateTimeImmutable($sInfo['finishAt'], $tz);

                    if (
                        $start < $announcement->getStartAt() || $start > $announcement->getFinishAt()
                        || $finish < $announcement->getStartAt() || $finish > $announcement->getFinishAt()
                    ) {
                        return $this->sendResponse([
                            'status' => Response::HTTP_ACCEPTED,
                            'error' => true,
                            'data' => ['ANNOUNCEMENT.FORM.GROUP.SESSION_DATE_MISMATCH'],
                        ]);
                    }

                    if ($finish <= $start) {
                        return $this->sendResponse([
                            'status' => Response::HTTP_ACCEPTED,
                            'error' => true,
                            'data' => ['ANNOUNCEMENT.FORM.GROUP.SESSION_DATE_MISMATCH'],
                        ]);
                    }

                    if ($prevFinishAt && $finish < $prevFinishAt) {
                        return $this->sendResponse([
                            'status' => Response::HTTP_ACCEPTED,
                            'error' => true,
                            'data' => ['ANNOUNCEMENT.FORM.GROUP.WRONG_DATE_BETWEEN_SESSIONS'],
                        ]);
                    }
                    $prevFinishAt = $finish;

                    $session->setAnnouncementGroup($group)
                        ->setStartAt($start)
                        ->setFinishAt($finish)
                        ->setSessionNumber(empty($sInfo['session_number']) ? 0 : $sInfo['session_number'])
                        ->setTimezone($timezone)
                        ->setCost((string) ($sInfo['cost'] ?? '0'))
                        ->setType($sInfo['type'] ?? TypeSession::TYPE_PRESENTIAL);

                    if (isset($d['typeMoney']) && !empty($d['typeMoney'])) {
                        $session->setTypeMoney($typeMoneyClass);
                    }

                    if (
                        TypeSession::TYPE_PRESENTIAL === $session->getType()
                        && (
                            self::DEFAULT_TYPE_COURSE === $typeCourse->getCode()
                            || TypeCourse::CODE_ONSITE_EXTERN === $typeCourse->getCode()
                            || TypeCourse::CODE_MIXED === $typeCourse->getCode()
                            || TypeCourse::CODE_MIXED_EXTERN === $typeCourse->getCode()
                        )
                    ) {
                        $entryMargin = empty($sInfo['entryMargin']) ? $this->settings->get('app.fundae.default_entry_margin') : $sInfo['entryMargin'];
                        $exitMargin = empty($sInfo['exitMargin']) ? $this->settings->get('app.fundae.default_exit_margin') : $sInfo['exitMargin'];

                        $place = $sInfo['place'] ?? $group->getPlace(); // If no place is defined, use the place defined in the group
                        $session->setEntryMargin($entryMargin)
                            ->setExitMargin($exitMargin)
                            ->setPlace($place);
                        $session->setType(TypeSession::TYPE_PRESENTIAL);

                        $modality = empty($sInfo['modality']) ? null : $sInfo['modality'];

                        if ($modality) {
                            $modalityClass = $this->em->getRepository(AnnouncementModality::class)->find($modality['id']);
                            $session->setModality($modalityClass);
                        }
                    }

                    if (
                        TypeSession::TYPE_VIRTUAL === $sInfo['type']
                        && (
                            TypeCourse::CODE_VIRTUAL_CLASSROOM === $typeCourse->getCode()
                            || TypeCourse::CODE_VIRTUAL_CLASSROOM_EXTERN === $typeCourse->getCode()
                            || TypeCourse::CODE_MIXED === $typeCourse->getCode()
                            || TypeCourse::CODE_MIXED_EXTERN === $typeCourse->getCode()
                        )
                    ) {
                        $session->setUrl($sInfo['url']);
                        $session->setType(TypeSession::TYPE_VIRTUAL);

                        $virtualMeetingTypePayload = [
                            'type' => $sInfo['virtual_meeting_type'] ?? null,
                            'url' => $sInfo['virtual_meeting_url'] ?? null,
                            'start_at' => $start->format(DateTimeTransformer::DATE_TIME_FORMAT),
                            'finish_at' => $finish->format(DateTimeTransformer::DATE_TIME_FORMAT),
                        ];

                        VirtualMeetingValidator::validateVirtualMeetingRequest($virtualMeetingTypePayload);

                        $virtualMeetingDto = VirtualMeetingDTOTransformer::fromPayload(
                            $virtualMeetingTypePayload
                        );

                        if (!$previousVirtualMeetingId) {
                            /** @var Uuid $virtualMeetingId */
                            $virtualMeetingId = $this->commandBus->execute(
                                new CreateVirtualMeeting($virtualMeetingDto)
                            );

                            $session->setVirtualMeetingId($virtualMeetingId->value());
                        } else {
                            $this->commandBus->execute(
                                new UpdateVirtualMeeting(
                                    virtualMeetingId: new Uuid($previousVirtualMeetingId),
                                    virtualMeetingDto: $virtualMeetingDto
                                )
                            );
                        }
                    } else {
                        $session->setVirtualMeetingId(null);
                    }

                    if (
                        $previousVirtualMeetingId
                        && null === $session->getVirtualMeetingId()
                    ) {
                        // Deleting virtual meeting info
                        $virtualMeetingId = new Uuid($previousVirtualMeetingId);

                        $this->commandBus->execute(
                            new DeleteVirtualMeeting($virtualMeetingId)
                        );
                    }

                    $sessions[] = $session;
                }

                $group->setAnnouncementGroupSessions($sessions);
            }

            $this->em->persist($group);
        }
        $this->em->flush();

        if (TypeCourse::CODE_ONLINE !== $typeCourse->getCode()) {
            // Generate all session tokens
            foreach ($announcement->getAnnouncementGroups() as $g) {
                foreach ($g->getAnnouncementGroupSessions() as $s) {
                    $entryToken = $this->em->getRepository(GenericToken::class)->createAnnouncementSessionToken($s);
                    $exitToken = $this->em->getRepository(GenericToken::class)->createAnnouncementSessionToken($s, GenericToken::TYPE_ANNOUNCEMENT_SESSION_EXIT_QR);
                }

                /**
                 * Reorder session numbers.
                 */
                $sessions = $this->em->getRepository(AnnouncementGroupSession::class)->findBy([
                    'announcementGroup' => $g,
                ], ['startAt' => 'ASC']);

                $sessionNumber = 1;
                foreach ($sessions as $s) {
                    $s->setSessionNumber($sessionNumber);
                    ++$sessionNumber;
                    $this->em->persist($s);
                }
                $this->em->flush();
            }
        }

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [],
        ]);
    }

    /**
     * @Rest\Post("/new-announcement-tutor")
     */
    public function saveNewUserTutor(Request $request): Response
    {
        try {
            $id = $request->get('id');
            $announcementGroup = $this->em->getRepository(AnnouncementGroup::class)->find($id);
            if (!$announcementGroup) {
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => 'Announcement group not found',
                ]);
            }

            $identification = $request->get('identification');
            $company = $request->get('company');
            $content = [
                'email' => $request->get('email'),
                'firstName' => $request->get('firstName'),
                'lastName' => $request->get('lastName'),
                'identificationValue' => $request->get('identificationValue'),
                'telephone' => $request->get('telephone'),
                'tutoringTime' => $request->get('tutoringTime'),
                'identification' => !empty($identification) ? json_decode($identification, true) : null,
                'company' => !empty($company) ? json_decode($company, true) : null,
                'locale' => $this->settings->get('app.adminDefaultLanguage'),
                'localeCampus' => $this->settings->get('app.defaultLanguage'),
            ];

            /** @var UploadedFile|null $cv */
            $cv = $request->files->get('cv');
            if (!$cv) {
                $cv = json_decode($request->get('cv'), true);
            }

            $result = $this->em->getRepository(AnnouncementTutor::class)->saveNewTutorUser(
                $announcementGroup,
                $content,
                $cv
            );

            return $this->sendResponse([
                'status' => $result ? Response::HTTP_CREATED : Response::HTTP_ACCEPTED,
                'error' => !$result,
                'data' => !$result ? 'Failed to save new tutor' :
                    $this->em->getRepository(AnnouncementTutor::class)
                    ->getTutorByGroup($announcementGroup),
            ]);
        } catch (\RuntimeException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Rest\Post("/group-tutor")
     *
     * @throws ManagerNotAuthorizedException
     */
    public function saveAnnouncementGroupTutorInfo(Request $request): Response
    {
        // TODO: Migrate to V2 CQRS handling and validation.
        $id = $request->get('id');
        if (empty($id)) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => '`id` is required',
            ]);
        }
        $announcementGroup = $this->em->getRepository(AnnouncementGroup::class)->find($id);
        if (!$announcementGroup) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'Announcement group not found',
            ]);
        }

        $announcement = $announcementGroup->getAnnouncement();

        /** @var User $user */
        $user = $this->getUser();
        $this->announcementAuthorizationService->ensureUserCanManageAnnouncement(
            user: $user,
            announcement: $announcement,
        );

        try {
            $identification = $request->get('identification');
            $company = $request->get('company');

            $content = [
                'tutorId' => $request->get('tutor-id'),
                'email' => $request->get('email'),
                'identificationValue' => $request->get('identificationValue'),
                'telephone' => $request->get('telephone'),
                'tutoringTime' => $request->get('tutoringTime'),
                'identification' => $identification ? json_decode($identification, true) : null,
                'company' => $company ? json_decode($company, true) : null,
            ];

            /** @var UploadedFile|null $cv */
            $cv = $request->files->get('cv');
            if (!$cv) {
                $cvRequest = $request->get('cv');
                if (null === $cvRequest) {
                    return $this->sendResponse([
                        'status' => Response::HTTP_ACCEPTED,
                        'error' => true,
                        'data' => '`cv` is required',
                    ]);
                }
                $cv = json_decode($request->get('cv'), true);
            }

            $result = $this->em->getRepository(AnnouncementTutor::class)->setTutorToAnnouncementGroup(
                $announcementGroup,
                $content,
                $cv
            );

            return $this->sendResponse([
                'status' => $result ? Response::HTTP_CREATED : Response::HTTP_ACCEPTED,
                'error' => !$result,
                'data' => !$result ? 'Failed to save tutor' :
                    $this->em->getRepository(AnnouncementTutor::class)
                    ->getTutorByGroup($announcementGroup, true),
            ]);
        } catch (\RuntimeException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Rest\Post("/communication")
     */
    public function saveAnnouncementCommunication(Request $request): Response
    {
        try {
            $content = json_decode($request->getContent(), true);
            $id = $content['id'];
            if (empty($id)) {
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => 'ID is required',
                ]);
            }

            $announcement = $this->em->getRepository(Announcement::class)->find($id);
            if (!$announcement) {
                return $this->sendResponse([
                    'status' => Response::HTTP_OK,
                    'error' => true,
                    'data' => 'Announcement not found',
                ]);
            }

            $configAnnouncement = $content['configAnnouncement'] ?? [];

            $isChatEnabled = \array_key_exists('configuration-' . AnnouncementConfigurationType::ID_ENABLE_CHAT, $configAnnouncement)
                && $configAnnouncement['configuration-' . AnnouncementConfigurationType::ID_ENABLE_CHAT];
            $isNotificationEnabled = \array_key_exists('configuration-' . AnnouncementConfigurationType::ID_ENABLE_NOTIFICATION, $configAnnouncement)
                && $configAnnouncement['configuration-' . AnnouncementConfigurationType::ID_ENABLE_NOTIFICATION];
            $isSMSEnabled = \array_key_exists('configuration-' . AnnouncementConfigurationType::ID_ENABLE_SMS, $configAnnouncement)
                && $configAnnouncement['configuration-' . AnnouncementConfigurationType::ID_ENABLE_SMS];
            $isForumEnabled = \array_key_exists('configuration-' . AnnouncementConfigurationType::ID_ENABLE_FORUM, $configAnnouncement)
                && $configAnnouncement['configuration-' . AnnouncementConfigurationType::ID_ENABLE_FORUM];

            $announcementConfigurationRepository = $this->em->getRepository(AnnouncementConfiguration::class);
            $announcementConfigurationRepository->enableChat($announcement, $isChatEnabled);
            $announcementConfigurationRepository->enableNotification($announcement, $isNotificationEnabled);
            $announcementConfigurationRepository->enableSMS($announcement, $isSMSEnabled);
            $announcementConfigurationRepository->enableForum($announcement, $isForumEnabled);

            $this->em->flush();

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [],
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => false,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Rest\Post("/certificate")
     *
     * @throws ManagerNotAuthorizedException
     */
    public function saveCertificateInfo(Request $request): Response
    {
        // TODO: Migrate to V2 CQRS handling and validation.
        $announcement = $this->getAnnouncementFromJsonRequest($request);
        if ($announcement instanceof Response) {
            return $announcement;
        }

        /** @var User $user */
        $user = $this->getUser();
        $this->announcementAuthorizationService->ensureUserCanManageAnnouncement(
            user: $user,
            announcement: $announcement,
        );

        $this->em->getRepository(AnnouncementConfiguration::class)
            ->enableCertificate(
                $announcement,
                $this->isConfigAnnouncementEnabled($request, AnnouncementConfigurationType::ID_ENABLE_CERTIFICATE)
            );

        $this->em->getRepository(AnnouncementConfiguration::class)
            ->enableObjetiveAndContentCertificate(
                $announcement,
                $this->isConfigAnnouncementEnabled($request, AnnouncementConfigurationType::ID_INCLUDE_OBJ_CONTENT_CERTIFICATE)
            );

        $this->em->getRepository(AnnouncementConfiguration::class)
            ->enableDniInCertificate(
                $announcement,
                $this->isConfigAnnouncementEnabled($request, AnnouncementConfigurationType::ID_INCLUDE_DNI_IN_CERTIFICATE)
            );

        $content = json_decode($request->getContent(), true);
        $isConfirmationRequiredDiploma = $content['isConfirmationRequiredDiploma'] ?? false;
        $typeDiploma = $content['typeDiploma'] ?? null;

        $announcement->setIsConfirmationRequiredDiploma($isConfirmationRequiredDiploma);

        if (!empty($typeDiploma)) {
            $type = $this->em->getRepository(TypeDiploma::class)->find($content['typeDiploma']);
            if ($type) {
                $announcement->setTypeDiploma($type);
            }
        }

        $this->em->persist($announcement);
        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [],
        ]);
    }

    /**
     * @Rest\Post("/survey")
     *
     * @throws ManagerNotAuthorizedException
     */
    public function saveSurveyInfo(Request $request): Response
    {
        // TODO: Migrate to V2 CQRS handling and validation.
        $announcement = $this->getAnnouncementFromJsonRequest($request);
        if ($announcement instanceof Response) {
            return $announcement;
        }

        /** @var User $user */
        $user = $this->getUser();
        $this->announcementAuthorizationService->ensureUserCanManageAnnouncement(
            user: $user,
            announcement: $announcement,
        );

        $isEnabled = $this->isConfigAnnouncementEnabled($request, AnnouncementConfigurationType::ID_ENABLE_SURVEY);
        $this->em->getRepository(AnnouncementConfiguration::class)
            ->enableSurvey(
                $announcement,
                $isEnabled
            );

        if ($isEnabled) {
            $content = json_decode($request->getContent(), true);
            $typeSurvey = $content['typeSurvey'] ?? null;
            if (!empty($typeSurvey)) {
                $survey = $this->em->getRepository(Survey::class)->find($typeSurvey);
                $surveyAnnouncement = $this->em->getRepository(SurveyAnnouncement::class)->findOneBy([
                    'announcement' => $announcement,
                ]);
                if (!$surveyAnnouncement) {
                    $surveyAnnouncement = new SurveyAnnouncement();
                    $surveyAnnouncement->setAnnouncement($announcement);
                }
                $surveyAnnouncement->setSurvey($survey);
                $this->em->persist($surveyAnnouncement);
            }
        } else {
            // Remove survey from announcement
            /** @var SurveyAnnouncement[] $selected */
            $selected = $this->em->getRepository(SurveyAnnouncement::class)->findBy(['announcement' => $announcement]);
            foreach ($selected as $s) {
                $this->em->remove($s);
            }
        }

        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [],
        ]);
    }

    /**
     * @Rest\Post("/alerts")
     */
    public function saveAnnouncementAlerts(Request $request): Response
    {
        $content = json_decode($request->getContent(), true);
        $announcement = $this->getAnnouncementFromJsonRequest($request);
        if ($announcement instanceof Response) {
            return $announcement;
        }

        /** @var User $user */
        $user = $this->getUser();
        $this->announcementAuthorizationService->ensureUserCanManageAnnouncement(
            user: $user,
            announcement: $announcement,
        );

        $configAnnouncement = $content['configAnnouncement'] ?? [];

        $isEnabled = \array_key_exists('configuration-' . AnnouncementConfigurationType::ID_ENABLE_ALERTS_TUTOR, $configAnnouncement)
            && $configAnnouncement['configuration-' . AnnouncementConfigurationType::ID_ENABLE_ALERTS_TUTOR];

        $this->em->getRepository(AnnouncementConfiguration::class)->enableAlertsTutor($announcement, $isEnabled);

        // Enable selected alerts.
        $alertTypeTutorValues = $content['alertTypeTutorValues'] ?? [];
        $alerts = [];
        foreach ($alertTypeTutorValues as $key => $value) {
            if ($value) {
                $id = explode('-', $key)[1];
                $alerts[] = $this->em->getRepository(AlertTypeTutor::class)->find($id);
            }
        }

        foreach ($announcement->getAnnouncementGroups() as $group) {
            $tutor = $group->getAnnouncementTutor();
            if (!$tutor) {
                continue;
            }
            $selected = [];
            foreach ($alerts as $alert) {
                $a = new AnnouncementAlertTutor();
                $a->setAlertTypeTutor($alert)
                    ->setAnnouncementTutor($tutor);

                $selected[] = $a;
            }
            $tutor->setAnnouncementAlertTutors($selected);
            $this->em->persist($tutor);
        }

        $this->em->flush();

        return $this->sendResponse([
            'status' => Response::HTTP_OK,
            'error' => false,
            'data' => [],
        ]);
    }

    /**
     * @Rest\Post("/user-from-file")
     *
     * @return Response|void
     *
     * @throws \Exception
     */
    public function saveUsersFromExcelFile(Request $request, KernelInterface $kernel)
    {
        try {
            /** @var UploadedFile|null $uploadedFile */
            $uploadedFile = $request->files->get('file');

            if (!$uploadedFile) {
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => ['ANNOUNCEMENT.FORM.USERS.UPLOAD_FILE_NOT_PROVIDED'],
                ]);
            }

            $filename = bin2hex(random_bytes(8)) . (new \DateTime())->getTimestamp() . '.xls';
            $path = $kernel->getProjectDir() . DIRECTORY_SEPARATOR . 'files/temp';
            $file = $uploadedFile->move($path, $filename);

            $fileType = IOFactory::identify($path . DIRECTORY_SEPARATOR . $filename);
            $reader = IOFactory::createReader($fileType);
            $reader->setReadDataOnly(true);
            $spreadsheet = $reader->load($path . DIRECTORY_SEPARATOR . $filename);

            $activeSheet = $spreadsheet->getActiveSheet();
            $lastRow = $activeSheet->getHighestDataRow();

            $userRepository = $this->em->getRepository(User::class);
            $usersToReturn = [];
            $failedUsers = [];
            for ($row = 2; $row <= $lastRow; ++$row) {
                $hasEnrollmentTemplate = $this->announcementConfigurationsService->hasEnrollmentTemplate();

                $employeeId = $activeSheet->getCell("A$row")->getValue(); // user.code
                $dni = null; // user.meta
                $hrp = null; // user.meta
                if ($hasEnrollmentTemplate) {
                    $hrp = $activeSheet->getCell("B$row")->getValue();
                } else {
                    $dni = $activeSheet->getCell("B$row")->getValue();
                }

                $email = $activeSheet->getCell("C$row")->getValue(); // user.email
                $name = $activeSheet->getCell("D$row")->getValue();
                $lastname = $activeSheet->getCell("E$row")->getValue();

                if (empty($employeeId) && empty($email) && empty($name) && empty($lastname) && empty($dni) && empty($hrp)) {
                    break; // Make sure to exit
                }
                if (empty($employeeId) && empty($hrp) && empty($dni) && empty($email)) {
                    continue;
                }

                // Validate mail
                if (!empty($email) && !filter_var($email, FILTER_VALIDATE_EMAIL)) {
                    return $this->sendResponse([
                        'status' => Response::HTTP_ACCEPTED,
                        'error' => true,
                        'data' => 'Invalid mail: ' . $email,
                    ]);
                }

                $user = null;

                // Find by code
                if (!empty($employeeId)) {
                    $user = $userRepository->findOneBy(['code' => $employeeId]);
                }

                // Find by HRP
                if (!$user && $hasEnrollmentTemplate && !empty($hrp)) {
                    $user = $userRepository->findUserIberostarWithHRP($hrp);
                }

                if (!$user && !empty($dni)) {
                    $user = $userRepository->findOneBy(['registerKey' => $dni]);
                }

                // Find by email
                if (!$user && !empty($email)) {
                    $user = $userRepository->findOneBy(['email' => $email]);
                }

                if (!$user) {
                    if (!empty($email)) {
                        $failedUsers[] = $email;
                    } elseif (!empty($employeeId)) {
                        $failedUsers[] = $employeeId;
                    } elseif (!empty($dni)) {
                        $failedUsers[] = $dni;
                    } else {
                        $failedUsers[] = $hrp;
                    }
                    continue;
                }

                $fieldsFundae = $user->getUserFieldsFundae();
                if (!$fieldsFundae) {
                    $fieldsFundae = new UserFieldsFundae();
                    $fieldsFundae->setUser($user);
                    $fieldsFundae->setDni($hasEnrollmentTemplate ? $hrp : $dni);
                    $user->setUserFieldsFundae($fieldsFundae);
                }

                $this->em->persist($user);

                $usersToReturn[] = $user;
            }

            $this->em->flush();

            $u = [];

            foreach ($usersToReturn as $user) {
                $u[] = [
                    'id' => $user->getId(),
                    'firstName' => $user->getFirstName(),
                    'lastName' => $user->getLastName(),
                    'email' => $user->getEmail(),
                ];
            }

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [
                    'data' => $u,
                    'not_found' => $failedUsers,
                ],
            ]);
        } catch (FileException $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'Failed to handle the file: ' . $e->getMessage(),
            ]);
        } catch (Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'Failed to read xlsx|xls file: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     *
     * @Rest\Post("/participants-from-file")
     *
     * @throws \Exception
     */
    public function importParticipantsFromExcelFile(Request $request, ValidatorInterface $validator): Response
    {
        // Optimized memory and execution settings for mass import
        // ini_set('memory_limit', '2048M'); // Increased for large datasets
        // ini_set('max_execution_time', '1800'); // 2 hours for very large imports

        // Enable garbage collection optimization
        gc_enable();
        gc_collect_cycles();

        try {
            $timezone = $this->getUserTimezone();

            $uploadedFile = $request->files->get('file');
            if (!$uploadedFile) {
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => ['ANNOUNCEMENT.FORM.USERS.UPLOAD_FILE_NOT_PROVIDED'],
                ]);
            }

            $fileConstraint = new Assert\File([
                'maxSize' => '10M', // Reduced from 50M to prevent CloudFlare timeout
                'mimeTypes' => [
                    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                    'application/vnd.ms-excel',
                ],
                'mimeTypesMessage' => 'Please upload a valid Excel file (.xlsx or .xls).',
            ]);

            $errors = $validator->validate($uploadedFile, $fileConstraint);

            if (\count($errors) > 0) {
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => ['ANNOUNCEMENT.FORM.USERS.INVALID_FILE_TYPE', 'details' => (string) $errors],
                ]);
            }
        } catch (\Exception $e) {
            $this->logger->error('Mass import: Validation error', [
                'error' => $e->getMessage(),
            ]);

            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }

        try {
            $originalFilename = $uploadedFile->getClientOriginalName();
            $fileSize = $uploadedFile->getSize();

            $filePath = $this->excelReportGeneratorService->moveUploadedFile($uploadedFile);
            $spreadsheet = $this->excelReportGeneratorService->loadSpreadsheet($filePath);

            $requiredColumnsAnnouncements = ['B', 'D', 'F', 'H', 'I', 'J', 'Q', 'O'];
            $requiredColumnsParticipants = ['F'];

            $sheetAnnouncements = $this->excelReportGeneratorService->validateExcelStructure($spreadsheet, 'Listado Formaciones', $requiredColumnsAnnouncements);
            $sheetParticipants = $this->excelReportGeneratorService->validateExcelStructure($spreadsheet, 'Participantes', $requiredColumnsParticipants);

            $totalAnnouncementRows = $sheetAnnouncements->getHighestDataRow() - 1;
            $totalParticipantRows = $sheetParticipants->getHighestDataRow() - 1;

            $this->logger->info('Mass import: Starting process', [
                'filename' => $originalFilename,
                'file_size' => $fileSize,
                'total_announcement_rows' => $totalAnnouncementRows,
                'total_participant_rows' => $totalParticipantRows,
            ]);

            $announcements = $this->externalAnnouncementImportService->processAnnouncements($sheetAnnouncements, $timezone);
            $this->em->flush();

            $this->externalAnnouncementParticipantImportService->processParticipants(
                participantsSheet: $sheetParticipants,
                announcements: $announcements
            );
            $this->em->flush();

            $this->logger->info('Mass import: Process completed successfully', [
                'total_announcements_created' => \count($announcements),
            ]);

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => 'Data logged successfully.',
            ]);
        } catch (\Exception $e) {
            $this->logger->error('Mass import: Critical error', [
                'error' => $e->getMessage(),
                'file' => $e->getFile(),
                'line' => $e->getLine(),
            ]);

            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @return Announcement|Response
     */
    private function getAnnouncementFromJsonRequest(Request $request)
    {
        $content = json_decode($request->getContent(), true);
        $id = $content['id'] ?? null;
        if (empty($id)) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'Announcement ID is required',
            ]);
        }

        $announcement = $this->em->getRepository(Announcement::class)->find($id);
        if (!$announcement) {
            return $this->sendResponse([
                'status' => Response::HTTP_ACCEPTED,
                'error' => true,
                'data' => 'Announcement not found',
            ]);
        }

        return $announcement;
    }

    /**
     * @Rest\Post("/group-info-session")
     *
     * @throws \Exception
     */
    public function saveClassroomSession(Request $request, ClassroomvirtualService $classroomvirtualService): Response
    {
        try {
            $content = json_decode($request->getContent(), true);

            /**  Handle session */
            $groupId = $content['groupId'] ?? null;
            $id = $content['id'] ?? null;

            $group = $this->em->getRepository(AnnouncementGroup::class)->find($groupId);
            if (!$group) {
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => 'Group not found',
                ]);
            }

            $announcement = $group->getAnnouncement();
            $timezone = empty($content['timezone']) ? $announcement->getTimezone() : $content['timezone'];
            $tz = new \DateTimeZone($timezone);

            $session = $this->em->getRepository(AnnouncementGroupSession::class)->find($id);

            if (!$session) {
                $session = new AnnouncementGroupSession();
                $session->setAnnouncementGroup($group);
            }

            $startAt = $content['startAt'] ?? null;
            $finishAt = $content['finishAt'] ?? null;

            if (empty($startAt) || empty($finishAt)) {
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => ['ANNOUNCEMENT.FORM.GROUP.SESSION_FIELDS_REQUIRED', 'ANNOUNCEMENT.FORM.GROUP.SESSION_DATE_MISMATCH'],
                ]);
            }

            $dtStartAt = new \DateTimeImmutable($startAt, $tz);
            $dtFinishAt = new \DateTimeImmutable($finishAt, $tz);

            if ($dtStartAt > $dtFinishAt) {
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => ['ANNOUNCEMENT.FORM.GROUP.SESSION_DATE_MISMATCH'],
                ]);
            }

            $session->setStartAt($dtStartAt)
                ->setFinishAt($dtFinishAt)
                ->setSessionNumber($content['session_number'] ?? 0)
                ->setTimezone($timezone);

            $this->em->persist($session);
            $this->em->flush();

            /** Handle virtual classroom */
            $providerId = $content['providerId'] ?? null;
            $type = $this->em->getRepository(ClassroomvirtualType::class)->find($providerId);

            if (!$type) {
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => 'Virtual Classroom type not found',
                ]);
            }

            $announcement = $group->getAnnouncement();
            $course = $announcement->getCourse();

            $virtualClassroom = empty($content['classroomId']) ? null : $this->em->getRepository(Classroomvirtual::class)->find($content['classroomId']);

            $diffInMinutes = $session->getSessionTimeInMinutes();

            $result = $classroomvirtualService->createRoom([
                'idTypeClassroom' => $type->getId(),
                'idAnnouncementGroup' => $group->getId(),
                'idAnnouncementGroupSession' => $session->getId(),
                'idClassroomvirtual' => $virtualClassroom ? $virtualClassroom->getId() : null,
                'name' => "Session {$session->getSessionNumber()}",
                'description' => $course->getName(),
                'startsat' => $dtStartAt->format('c'), // Allow full timezone datetime
                'duration' => $diffInMinutes * 60, // In seconds
                'timezone' => $session->getTimezone(),
                'sessionNumber' => $session->getSessionNumber(),
            ]);

            if (Response::HTTP_OK !== $result['status']) {
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'error' => true,
                    'data' => $result['data'],
                ]);
            }

            $session->setUrl($result['tutor_url']);

            $this->em->persist($session);
            $this->em->flush();

            $classroom = $session->getClassroomvirtual();

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => [
                    'id' => $session->getId(),
                    'startAt' => substr($session->getStartAt()->format('c'), 0, 16),
                    'finishAt' => substr($session->getFinishAt()->format('c'), 0, 16),
                    'timezone' => $timezone,
                    'url' => $session->getUrl(),
                    'classroomId' => $classroom ? $classroom->getId() : null,
                ],
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    /**
     * @Rest\Post("/delete-group-info-session")
     */
    public function deleteAnnouncementGroupSession(Request $request, ClassroomvirtualService $classroomvirtualService): Response
    {
        try {
            $content = json_decode($request->getContent(), true);
            $sessionId = $content['id'] ?? null;
            if (empty($sessionId)) {
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'code' => Response::HTTP_BAD_REQUEST,
                    'error' => true,
                    'data' => 'id is required',
                ]);
            }

            $session = $this->em->getRepository(AnnouncementGroupSession::class)->find($sessionId);
            if (!$session) {
                return $this->sendResponse([
                    'status' => Response::HTTP_ACCEPTED,
                    'code' => Response::HTTP_NOT_FOUND,
                    'error' => true,
                    'data' => 'Session not found',
                ]);
            }

            $announcement = $session->getAnnouncementGroup()->getAnnouncement();

            $currentDate = new \DateTimeImmutable();
            if ($announcement->getNotifiedAt()) {
                /* Announcement already notified, validate if session can be modified or deleted */
                if ($session->getFinishAt()->getTimestamp() < $currentDate->getTimestamp()) {
                    return $this->sendResponse([
                        'status' => Response::HTTP_ACCEPTED,
                        'error' => true,
                        'data' => 'Not allowed',
                    ]);
                }
            }

            $room = $session->getClassroomvirtual();
            if ($room) {
                $result = $classroomvirtualService->deleteRoom([
                    'idClassroomvirtual' => $room->getId(),
                ]);
            }

            $virtualMeetingId = $session->getVirtualMeetingId();
            if ($virtualMeetingId) {
                $this->commandBus->execute(
                    new DeleteVirtualMeeting(new Uuid($virtualMeetingId))
                );
            }

            $this->em->remove($session);
            $this->em->flush();

            return $this->sendResponse([
                'status' => Response::HTTP_OK,
                'error' => false,
                'data' => 'Room removed',
            ]);
        } catch (\Exception $e) {
            return $this->sendResponse([
                'status' => Response::HTTP_INTERNAL_SERVER_ERROR,
                'error' => true,
                'data' => $e->getMessage(),
            ]);
        }
    }

    private function isConfigAnnouncementEnabled(Request $request, int $id)
    {
        $content = json_decode($request->getContent(), true);
        $configAnnouncement = $content['configAnnouncement'] ?? [];

        return \array_key_exists("configuration-{$id}", $configAnnouncement) && $configAnnouncement["configuration-{$id}"];
    }

    /**
     * @IsGranted("ROLE_SUPER_ADMIN")
     *
     * @Rest\Get("/template-participants")
     */
    public function downloadParticipantsTemplate(KernelInterface $kernel): Response
    {
        $projectDir = $kernel->getProjectDir();
        $filePath = $projectDir . '/public/assets/templates/announcement_import_template.xlsx';

        if (!file_exists($filePath)) {
            throw $this->createNotFoundException('El archivo de la plantilla no existe.');
        }

        return new BinaryFileResponse($filePath);
    }

    public function getUserTimezone(): string
    {
        $user = $this->security->getUser();

        if ($user instanceof User && $user->getTimezone()) {
            return $user->getTimezone();
        }

        return $this->settings->get('app.default_timezone');
    }
}
