<?php

declare(strict_types=1);

namespace App\Service;

use App\Entity\Setting;
use Doctrine\ORM\EntityManagerInterface;
use Symfony\Component\DependencyInjection\ParameterBag\ParameterBagInterface;

class SettingsService
{
    private EntityManagerInterface $em;
    private ParameterBagInterface $params;
    private array $settings = [];

    public function __construct(EntityManagerInterface $em, ParameterBagInterface $params)
    {
        $this->em = $em;
        $this->params = $params;

        $schemaManager = $em->getConnection()->getSchemaManager();
        if ($schemaManager->tablesExist(['setting'])) {
            $this->loadSettings();
        }
    }

    public function loadSettings(): self
    {
        $settings = $this->em->getRepository(Setting::class)->findAll();

        foreach ($settings as $setting) {
            $this->settings[$setting->getCode()] = $setting->getTransformedValue();
        }

        return $this;
    }

    public function getSettings(): array
    {
        return $this->settings;
    }

    public function get(string $key)
    {
        return $this->getSetting($key);
    }

    public function getSettingOptions(string $key)
    {
        $options = [];
        $setting = $this->em->getRepository(Setting::class)->findOneBy(['code' => $key]);
        if ($setting) {
            $options = $setting->getOptions();
        }

        return $options;
    }

    public function getSetting(string $key)
    {
        if (isset($this->settings[$key])) {
            return $this->settings[$key];
        } elseif ($this->params->has($key)) {
            return $this->params->get($key);
        } else {
            return null;
        }
    }

    public function setSetting(string $key, $value): self
    {
        $setting = $this->em->getRepository(Setting::class)->findOneBy(['code' => $key]);

        if (!$setting) {
            $setting = new Setting();
            $setting->setCode($key);
        }

        $setting->setValue($value);
        $this->em->persist($setting);
        $this->em->flush();

        $this->settings[$key] = $setting->getTransformedValue();

        return $this;
    }
}
