<?php

declare(strict_types=1);

namespace App\V2\Application\QueryHandler;

use App\V2\Application\Query\GetVirtualMeetings;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\VirtualMeeting\PaginatedVirtualMeetingCollection;
use App\V2\Domain\VirtualMeeting\VirtualMeetingRepository;

readonly class GetVirtualMeetingsQueryHandler
{
    public function __construct(
        private VirtualMeetingRepository $virtualMeetingRepository,
    ) {
    }

    /**
     * @throws InfrastructureException
     */
    public function handle(GetVirtualMeetings $query): PaginatedVirtualMeetingCollection
    {
        $collection = $this->virtualMeetingRepository->findBy($query->getCriteria());
        $totalItems = $this->virtualMeetingRepository->countBy($query->getCriteria());

        return new PaginatedVirtualMeetingCollection(
            collection: $collection,
            totalItems: $totalItems
        );
    }
}
