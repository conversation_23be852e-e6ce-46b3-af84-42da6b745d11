<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\VirtualMeeting;

use App\V2\Domain\VirtualMeeting\VirtualMeetingType;

class VirtualMeetingTypeTransformer
{
    public static function fromVirtualMeetingTypeToString(VirtualMeetingType $type): string
    {
        return match ($type) {
            VirtualMeetingType::Fixed => 'fixed',
            default => throw new \InvalidArgumentException(\sprintf('Invalid virtual meeting type: %s', $type->name)),
        };
    }
}
