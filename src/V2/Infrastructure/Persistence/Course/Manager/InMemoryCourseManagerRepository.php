<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\Course\Manager;

use App\V2\Domain\Course\Manager\CourseManager;
use App\V2\Domain\Course\Manager\CourseManagerCollection;
use App\V2\Domain\Course\Manager\CourseManagerCriteria;
use App\V2\Domain\Course\Manager\CourseManagerRepository;
use App\V2\Domain\Shared\Collection\CollectionException;

class InMemoryCourseManagerRepository implements CourseManagerRepository
{
    private CourseManagerCollection $collection;

    /**
     * @throws CollectionException
     */
    public function __construct()
    {
        $this->collection = new CourseManagerCollection([]);
    }

    /**
     * @throws CollectionException
     */
    public function add(CourseManager $courseManager): void
    {
        $this->collection->append($courseManager);
    }

    #[\Override]
    public function findBy(CourseManagerCriteria $criteria): CourseManagerCollection
    {
        return $this->filterByCriteria($criteria);
    }

    /**
     * @throws CollectionException
     */
    private function filterByCriteria(CourseManagerCriteria $criteria): CourseManagerCollection
    {
        $collection = $this->collection->filter(
            fn (CourseManager $courseManager) => (
                null === $criteria->getUserId()
                || $criteria->getUserId()->equals($courseManager->getUserId())
            )
            && (
                null === $criteria->getCourseId()
                || $criteria->getCourseId()->equals($courseManager->getCourseId())
            )
        );

        return $collection->map(
            fn (CourseManager $courseManager) => new CourseManager(
                userId: $courseManager->getUserId(),
                courseId: $courseManager->getCourseId(),
            )
        );
    }
}
