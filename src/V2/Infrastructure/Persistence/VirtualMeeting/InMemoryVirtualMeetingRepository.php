<?php

declare(strict_types=1);

namespace App\V2\Infrastructure\Persistence\VirtualMeeting;

use App\V2\Domain\Shared\Collection\CollectionException;
use App\V2\Domain\Shared\Exception\InfrastructureException;
use App\V2\Domain\Shared\Uuid\Uuid;
use App\V2\Domain\VirtualMeeting\Exception\VirtualMeetingNotFoundException;
use App\V2\Domain\VirtualMeeting\VirtualMeeting;
use App\V2\Domain\VirtualMeeting\VirtualMeetingCollection;
use App\V2\Domain\VirtualMeeting\VirtualMeetingCriteria;
use App\V2\Domain\VirtualMeeting\VirtualMeetingRepository;
use App\V2\Infrastructure\Persistence\InMemoryCommonCriteriaBuilder;

class InMemoryVirtualMeetingRepository implements VirtualMeetingRepository
{
    private VirtualMeetingCollection $collection;

    /**
     * @throws CollectionException
     */
    public function __construct()
    {
        $this->collection = new VirtualMeetingCollection([]);
    }

    #[\Override]
    public function put(VirtualMeeting $virtualMeeting): void
    {
        $meetings = $this->collection->allIndexedById();
        $meetings[$virtualMeeting->getId()->value()] = clone $virtualMeeting;
        $this->collection->replace($meetings);
    }

    #[\Override]
    public function findOneBy(VirtualMeetingCriteria $criteria): VirtualMeeting
    {
        $result = $this->filterByCriteria($criteria);

        if ($result->isEmpty()) {
            throw new VirtualMeetingNotFoundException();
        }

        return $result->first();
    }

    #[\Override]
    public function findBy(VirtualMeetingCriteria $criteria): VirtualMeetingCollection
    {
        return $this->filterByCriteria($criteria);
    }

    /**
     * @throws InfrastructureException
     */
    #[\Override]
    public function countBy(VirtualMeetingCriteria $criteria): int
    {
        return $this->filterByCriteria($criteria)->count();
    }

    #[\Override]
    public function delete(VirtualMeeting $virtualMeeting): void
    {
        $meetings = $this->collection->allIndexedById();

        if (!isset($meetings[$virtualMeeting->getId()->value()])) {
            throw new VirtualMeetingNotFoundException();
        }

        unset($meetings[$virtualMeeting->getId()->value()]);
        $this->collection->replace($meetings);
    }

    /**
     * @throws CollectionException
     */
    private function filterByCriteria(VirtualMeetingCriteria $criteria): VirtualMeetingCollection
    {
        /** @var VirtualMeetingCollection $collection */
        $collection = InMemoryCommonCriteriaBuilder::filterByCommonCriteria($criteria, $this->collection);

        // Filter by type
        if (null !== $criteria->getType()) {
            $collection = $collection->filter(
                fn (VirtualMeeting $meeting): bool => $meeting->getType() === $criteria->getType()
            );
        }

        // Filter by startAt range
        if (null !== $criteria->getStartAtFrom()) {
            $collection = $collection->filter(
                fn (VirtualMeeting $meeting): bool => $meeting->getStartAt() >= $criteria->getStartAtFrom()
            );
        }

        if (null !== $criteria->getStartAtTo()) {
            $collection = $collection->filter(
                fn (VirtualMeeting $meeting): bool => $meeting->getStartAt() <= $criteria->getStartAtTo()
            );
        }

        // Filter by finishAt range
        if (null !== $criteria->getFinishAtFrom()) {
            $collection = $collection->filter(
                fn (VirtualMeeting $meeting): bool => $meeting->getFinishAt() >= $criteria->getFinishAtFrom()
            );
        }

        if (null !== $criteria->getFinishAtTo()) {
            $collection = $collection->filter(
                fn (VirtualMeeting $meeting): bool => $meeting->getFinishAt() <= $criteria->getFinishAtTo()
            );
        }

        return $collection->map(
            function (VirtualMeeting $meeting) {
                /** @var Uuid $id */
                $id = $meeting->getId();

                return new VirtualMeeting(
                    id: $id,
                    type: $meeting->getType(),
                    startAt: $meeting->getStartAt(),
                    finishAt: $meeting->getFinishAt(),
                    url: $meeting->getUrl(),
                    createdAt: $meeting->getCreatedAt(),
                    updatedAt: $meeting->getUpdatedAt(),
                    deletedAt: $meeting->getDeletedAt(),
                );
            }
        );
    }
}
