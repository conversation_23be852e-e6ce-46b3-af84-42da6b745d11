<?php

declare(strict_types=1);

namespace App\V2\Domain\Course\Manager;

use App\V2\Domain\Shared\Entity\Entity;
use App\V2\Domain\Shared\Id\Id;

readonly class CourseManager implements Entity
{
    public function __construct(
        private Id $userId,
        private Id $courseId,
    ) {
    }

    public function getCourseId(): Id
    {
        return $this->courseId;
    }

    public function getUserId(): Id
    {
        return $this->userId;
    }
}
