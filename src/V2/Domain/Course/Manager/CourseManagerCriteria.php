<?php

declare(strict_types=1);

namespace App\V2\Domain\Course\Manager;

use App\V2\Domain\Shared\Criteria\Criteria;
use App\V2\Domain\Shared\Id\Id;

/**
 * @extends Criteria<CourseManagerCriteria>
 */
class CourseManagerCriteria extends Criteria
{
    private ?Id $userId = null;
    private ?Id $courseId = null;

    public function isEmpty(): bool
    {
        return null === $this->userId && null === $this->courseId;
    }

    public function filterByUserId(Id $id): self
    {
        $this->userId = $id;

        return $this;
    }

    public function filterByCourseId(Id $id): self
    {
        $this->courseId = $id;

        return $this;
    }

    public function getUserId(): ?Id
    {
        return $this->userId;
    }

    public function getCourseId(): ?Id
    {
        return $this->courseId;
    }
}
