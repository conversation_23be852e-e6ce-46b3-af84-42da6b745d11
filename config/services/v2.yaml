parameters:
  app.admin_vue3: '%env(bool:ADMIN_VUE_3)%'
  app.currency_code: '%env(CURRENCY_CODE)%'
  app.tax_rate: '%env(TAX_RATE)%'
  app.security_config:
    prefix: '/api/v2'
    # Rules order is strict, first found rule is the rule to be applied, by default requires to be authenticated and
    # with the ROLE_ADMIN role to avoid security breaches
    access_control:
      - { path: ^/api/v2/locales, roles: [ PUBLIC_ACCESS ] }
      - { path: ^/api/v2/time-zones, roles: [ ROLE_ADMIN, ROLE_MANAGER ] }
      - { path: ^/api/v2/admin/lti, roles: [ ROLE_SUPER_ADMIN ] }
      - { path: '^/api/v2/admin/courses/[\w-]+/chapter/[\w-]+/lti-launch', roles: [ ROLE_USER ] }
      - { path: '^/api/v2/campus/courses/[\w-]+/chapter/[\w-]+/lti-launch', roles: [ ROLE_USER ] }
      - { path: ^/api/v2/purchases, roles: [ ROLE_USER ] }
      - { path: '^/api/v2/admin/courses/[\w-]+/creators/[\w-]+', roles: [ROLE_SUPER_ADMIN, ROLE_ADMIN, ROLE_CREATOR] }
      - { path: '^/api/v2/admin/announcements/[\w-]+/managers/[\w-]+', roles: [ROLE_SUPER_ADMIN, ROLE_ADMIN, ROLE_MANAGER] }
      - { path: '^/api/v2/admin/announcements/[\w-]+/groups/[\w-]+/user/[\w-]+', roles: [ROLE_SUPER_ADMIN, ROLE_ADMIN, ROLE_MANAGER] }
      - { path: ^/api/v2/admin/purchasable-items, roles: [ROLE_SUPER_ADMIN, ROLE_ADMIN] }
      - { path: ^/api/v2/admin/filters/categories, roles: [ROLE_SUPER_ADMIN, ROLE_ADMIN, ROLE_MANAGER] }
      - { path: ^/api/v2/admin/filters, roles: [ROLE_SUPER_ADMIN, ROLE_ADMIN, ROLE_MANAGER] }
      - { path: '^/api/v2/admin/users/[\w-]+/manager-filters', roles: [ ROLE_ADMIN ] }
      - { path: '^/api/v2/admin/users/[\w-]+/filters', roles: [ ROLE_MANAGER ] }
      - { path: ^/api/v2/admin/users/managers, roles: [ROLE_SUPER_ADMIN, ROLE_ADMIN, ROLE_MANAGER] }
      - { path: ^/api/v2/admin/roles, roles: [ ROLE_ADMIN, ROLE_SUPER_ADMIN, ROLE_MANAGER ] }
      - { path: ^/api/v2/admin, roles: [ ROLE_ADMIN, ROLE_SUPER_ADMIN, ROLE_MANAGER, ROLE_CREATOR ] }

services:
  _defaults:
    autowire: true      # Automatically injects dependencies in your services.
    autoconfigure: true # Automatically registers your services as commands, event subscribers, etc.
    bind:
      # Log
      $logPath: "%kernel.logs_dir%"

      # JWT
      int $jwtTokenTtl: '%env(JWT_TOKENTTL)%'

      # Database table names
      string $courseCreatorTableName: 'course_creator'
      string $courseManagerTableName: 'course_manager'
      string $announcementManagerTableName: 'announcement_manager'
      string $purchasableItemTableName: 'purchasable_item'
      string $purchaseTableName: 'purchase'
      string $purchaseItemTableName: 'purchase_item'
      string $subscriptionTableName: 'subscription'
      string $ltiRegistrationTableName: 'lti_registration'
      string $ltiPlatformTableName: 'lti_platform'
      string $ltiToolTableName: 'lti_tool_v2' # To avoid collision with current lti_tool table
      string $ltiDeploymentTableName: 'lti_deployment'
      string $filterTableName: 'filter'
      string $filterCategoryTableName: 'filter_category'
      string $userFilterTableName: 'user_filter'
      string $managerFilterTableName: 'manager_filter'
      string $virtualMeetingTableName: 'virtual_meeting'


      ## LTI V2
      string $ltiKeysDir: '%kernel.project_dir%/config/secrets/lti1p3/'

  App\V2\:
    resource: '../../src/V2/'

  App\V2\Application\CommandHandler\:
    resource: '../../src/V2/Application/CommandHandler/'
    tags:
      - { name: 'tactician.handler', typehints: true }

  App\V2\Application\QueryHandler\:
    resource: '../../src/V2/Application/QueryHandler/'
    tags:
      - { name: 'tactician.handler', typehints: true }


  App\V2\Infrastructure\Controller\:
    resource: '../../src/V2/Infrastructure/Controller/'
    tags: [ 'controller.service_arguments' ]

  ########## V2 Domain repositories ##############
  App\V2\Domain\User\UserRepository:
    class: App\V2\Infrastructure\Persistence\User\DoctrineUserRepository
    arguments:
      - '@doctrine.orm.entity_manager'

  App\V2\Domain\Course\CourseRepository:
    class: App\V2\Infrastructure\Persistence\Course\DoctrineCourseRepository
    arguments:
      - '@doctrine.orm.entity_manager'

  App\V2\Domain\Course\Creator\CourseCreatorRepository:
    alias: App\V2\Infrastructure\Persistence\Course\Creator\DBALCourseCreatorRepository

  App\V2\Domain\Course\Manager\CourseManagerRepository:
    alias: App\V2\Infrastructure\Persistence\Course\Manager\DBALCourseManagerRepository

  App\V2\Domain\Security\RefreshTokenRepository:
    alias: App\V2\Infrastructure\Persistence\Security\DoctrineRefreshTokenRepository

  App\V2\Domain\Announcement\Manager\AnnouncementManagerRepository:
    alias: App\V2\Infrastructure\Persistence\Announcement\Manager\DBALAnnouncementManagerRepository

  App\V2\Domain\Purchase\PurchasableItemRepository:
    alias: App\V2\Infrastructure\Persistence\Purchase\DBALPurchasableItemRepository

  App\V2\Domain\Purchase\PurchaseRepository:
    alias: App\V2\Infrastructure\Persistence\Purchase\DBALPurchaseRepository

  App\V2\Domain\Subscription\SubscriptionRepository:
    alias: App\V2\Infrastructure\Persistence\Subscription\DBALSubscriptionRepository

  App\V2\Domain\Filter\FilterCategory\FilterCategoryRepository:
    alias: App\V2\Infrastructure\Persistence\Filter\FilterCategory\DBALFilterCategoryRepository

  App\V2\Domain\Filter\FilterRepository:
    alias: App\V2\Infrastructure\Persistence\Filter\DBALFilterRepository

  App\V2\Domain\User\UserFilter\UserFilterRepository:
    alias: App\V2\Infrastructure\Persistence\User\UserFilter\DBALUserFilterRepository

  App\V2\Domain\User\ManagerFilter\ManagerFilterRepository:
    alias: App\V2\Infrastructure\Persistence\User\ManagerFilter\DBALManagerFilterRepository

  App\V2\Domain\VirtualMeeting\VirtualMeetingRepository:
    alias: App\V2\Infrastructure\Persistence\VirtualMeeting\DBALVirtualMeetingRepository

  # AdminUrlGenerator wrapper for better testability
  App\V2\Application\Admin\LegacyAdminUrlGeneratorInterface:
    alias: App\V2\Infrastructure\Admin\EasyAdminLegacyUrlGenerator

  ## LTI Repositories
  App\V2\Domain\LTI\LtiRegistrationRepository:
    alias: App\V2\Infrastructure\Persistence\LTI\DBALLtiRegistrationRepository
  App\V2\Domain\LTI\LtiDeploymentRepository:
    alias: App\V2\Infrastructure\Persistence\LTI\DBALLtiDeploymentRepository
  App\V2\Domain\LTI\LtiToolRepository:
    alias: App\V2\Infrastructure\Persistence\LTI\DBALLtiToolRepository
  App\V2\Domain\LTI\LtiPlatformRepository:
    alias: App\V2\Infrastructure\Persistence\LTI\DBALLtiPlatformRepository


  ######### Hydrators #####################
  App\V2\Application\Hydrator\Course\Creator\CourseCreatorUserHydrator:
  App\V2\Application\Hydrator\Course\Creator\CourseCreatorHydratorCollection:
    autowire: true
    arguments:
      $hydrators:
        - '@App\V2\Application\Hydrator\Course\Creator\CourseCreatorUserHydrator'

  App\V2\Application\Hydrator\Announcement\Manager\AnnouncementManagerHydrator:
  App\V2\Application\Hydrator\Announcement\Manager\AnnouncementManagerHydratorCollection:
    autowire: true
    arguments:
      $hydrators:
        - '@App\V2\Application\Hydrator\Announcement\Manager\AnnouncementManagerHydrator'

  App\V2\Application\Hydrator\User\UserFilter\UserFilterHydrator:
  App\V2\Application\Hydrator\User\UserFilter\UserFilterHydratorCollection:
    autowire: true
    arguments:
      $hydrators:
        - '@App\V2\Application\Hydrator\User\UserFilter\UserFilterHydrator'

  App\V2\Application\Hydrator\User\ManagerFilter\ManagerFilterFilterHydrator:
  App\V2\Application\Hydrator\User\ManagerFilter\ManagerFilterHydratorCollection:
    autowire: true
    arguments:
      $hydrators:
        - '@App\V2\Application\Hydrator\User\ManagerFilter\ManagerFilterFilterHydrator'

  App\V2\Application\Hydrator\LTI\LtiRegistrationDeploymentHydrator:
  App\V2\Application\Hydrator\LTI\LtiRegistrationPlatformHydrator:
  App\V2\Application\Hydrator\LTI\LtiRegistrationToolHydrator:
  App\V2\Application\Hydrator\LTI\LtiRegistrationHydratorCollection:
    arguments:
      - - '@App\V2\Application\Hydrator\LTI\LtiRegistrationDeploymentHydrator'
        - '@App\V2\Application\Hydrator\LTI\LtiRegistrationPlatformHydrator'
        - '@App\V2\Application\Hydrator\LTI\LtiRegistrationToolHydrator'

  App\V2\Application\Hydrator\Purchase\PurchasePurchaseItemHydrator:
  App\V2\Application\Hydrator\Purchase\PurchaseHydratorCollection:
    arguments:
      - - '@App\V2\Application\Hydrator\Purchase\PurchasePurchaseItemHydrator'

  App\V2\Application\Hydrator\Purchase\PurchaseItemPurchasableItemHydrator:
  App\V2\Application\Hydrator\Purchase\PurchaseItemHydratorCollection:
    arguments:
      - - '@App\V2\Application\Hydrator\Purchase\PurchaseItemPurchasableItemHydrator'

  ######## Services ###############
  App\V2\Infrastructure\Log\MonologLogger:
    arguments:
      $logPath: "%kernel.logs_dir%"

  App\V2\Application\Log\Logger:
    alias: 'App\V2\Infrastructure\Log\MonologLogger'

  App\V2\Infrastructure\Utils\MpdfFactory:
    arguments:
      $cacheDir: "%kernel.cache_dir%"

  # Security
  App\V2\Infrastructure\Security\Firewall:
    arguments:
      $security: '%app.security_config%'
      $routeChecker: '@App\V2\Infrastructure\Service\RouteMethodChecker'
      $roleHierarchy: '%security.role_hierarchy.roles%'
    public: true

  ## LTI V2
  App\V2\Infrastructure\LTI\OpenSSLKeyProvider:
  App\V2\Domain\LTI\LtiKeyProvider:
    alias: App\V2\Infrastructure\LTI\OpenSSLKeyProvider


  App\V2\Infrastructure\Security\LexitJwtToken:
  App\V2\Domain\Security\TokenInterface:
    alias: App\V2\Infrastructure\Security\LexitJwtToken



  ## LTI Registration Repository
  App\V2\Infrastructure\Persistence\LTI\RegistrationRepository:
  App\V2\Infrastructure\Persistence\LTI\KeyChainRepository:
  App\V2\Infrastructure\LTI\UserAuthenticator:
  # Uncomment when LTI V2 development is completed
  #  OAT\Library\Lti1p3Core\Registration\RegistrationRepositoryInterface:
  #    alias: App\V2\Infrastructure\LTI\RegistrationRepository
  #
  #  OAT\Library\Lti1p3Core\Security\Key\KeyChainRepositoryInterface:
  #    alias: App\V2\Infrastructure\LTI\KeyChainRepository
  #
  #  OAT\Library\Lti1p3Core\Security\User\UserAuthenticatorInterface:
  #    alias: App\V2\Infrastructure\LTI\UserAuthenticator


  # Resource Providers
  App\V2\Application\Resource\:
    resource: '../../src/V2/Application/Resource/'
    tags: [ 'app.resource_provider' ]

  App\V2\Application\Resource\CompositeResourceProvider:
    arguments:
      $providers: !tagged_iterator app.resource_provider

  App\V2\Domain\Shared\Resource\ResourceProvider:
    alias: App\V2\Application\Resource\CompositeResourceProvider


  # Purchase Adapters
  App\V2\Application\Purchase\PurchasableFactory\:
    resource: '../../src/V2/Application/Purchase/PurchasableFactory/'
    tags: [ 'app.purchasable_item_factory' ]

  App\V2\Application\Purchase\PurchasableItemAdapter:
    arguments:
      $factories: !tagged_iterator app.purchasable_item_factory
